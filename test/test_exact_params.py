#!/usr/bin/env python3
"""
使用您提供的确切参数测试
"""

import requests
import json

def test_exact_params():
    """使用您提供的确切参数测试"""
    print("🎯 使用确切参数测试 Podcast API")
    print("=" * 60)
    
    # 您提供的确切参数
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3",
        "cover_image_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   音频URL: {payload['enclosure_url']}")
    print(f"   封面图URL: {payload['cover_image_url']}")
    print(f"   发布时间: {payload['pub_date']}")
    print(f"   Length参数: 未提供（使用默认值）")
    
    print(f"\n📋 完整请求体:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    
    try:
        print("\n⏳ 正在发送请求...")
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=120
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("\n🎉 成功创建 Podcast 条目！")
                print(f"   消息: {result['message']}")
                print(f"   标题: {result['title']}")
                print(f"   XML文件: {result['xml_file']}")
                print(f"   仓库: {result['repository']}")
                
                print("\n✅ 请检查以下内容:")
                print("   1. GitHub 仓库是否已更新")
                print("   2. podcast.xml 文件是否包含新条目")
                print("   3. 封面图信息是否正确记录（当前版本暂时禁用）")
                
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                print(f"📥 原始响应: {response.text}")
                return False
        else:
            print("❌ 创建失败")
            print(f"📥 响应内容: {response.text}")
            
            try:
                error_info = response.json()
                print(f"📥 错误信息: {json.dumps(error_info, indent=2, ensure_ascii=False)}")
            except:
                print("📥 无法解析错误信息为 JSON")
            
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时（120秒）")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器运行在 http://127.0.0.1:7899")
        return False
    except Exception as e:
        print(f"❌ 服务器检查异常: {e}")
        return False

if __name__ == "__main__":
    print("🧪 确切参数测试工具")
    print("=" * 70)
    
    # 检查服务器状态
    if not check_server_status():
        print("\n💡 请确保:")
        print("   1. 服务器已启动: python cmd_server.py")
        print("   2. 服务器运行在端口 7899")
        print("   3. 没有防火墙阻止连接")
        exit(1)
    
    print("\n🎯 开始测试您提供的确切参数...")
    
    # 执行测试
    success = test_exact_params()
    
    # 总结
    print("\n" + "=" * 70)
    if success:
        print("🎉 测试成功！")
        print("✅ Podcast 条目已创建")
        print("✅ 更改已推送到 GitHub")
        print("\n📋 后续步骤:")
        print("   1. 检查 GitHub 仓库: https://github.com/fjxc2021/podcast-feed")
        print("   2. 验证 podcast.xml 文件内容")
        print("   3. 确认新条目已正确添加")
        print("\n💡 注意: 封面图功能当前暂时禁用，等待修复")
    else:
        print("❌ 测试失败")
        print("\n🔍 故障排除建议:")
        print("   1. 检查服务器控制台的错误日志")
        print("   2. 确认 Git 配置和权限")
        print("   3. 验证网络连接")
        print("   4. 检查参数格式是否正确")
