#!/usr/bin/env python3
"""
直接测试 add_podcast_item 函数
"""

import sys
import os
import tempfile

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入服务器模块中的函数
try:
    from cmd_server import add_podcast_item
    print("✅ 成功导入 add_podcast_item 函数")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    exit(1)

def test_add_podcast_item():
    """测试 add_podcast_item 函数"""
    print("🧪 测试 add_podcast_item 函数")
    print("=" * 50)
    
    # 创建临时文件
    temp_dir = tempfile.mkdtemp()
    xml_file_path = os.path.join(temp_dir, "test_podcast.xml")
    
    print(f"📁 临时目录: {temp_dir}")
    print(f"📄 XML文件路径: {xml_file_path}")
    
    # 测试参数
    title = "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!"
    description = "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks."
    enclosure_url = "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3"
    cover_image_url = "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png"
    pub_date = "Fri, 09 May 2025 07:34:01 GMT"
    length = "12345678"
    
    print("📤 测试参数:")
    print(f"   标题: {title}")
    print(f"   描述长度: {len(description)} 字符")
    print(f"   音频URL: {enclosure_url}")
    print(f"   封面图URL: {cover_image_url}")
    print(f"   发布时间: {pub_date}")
    print(f"   文件大小: {length}")
    
    try:
        print("\n⏳ 调用 add_podcast_item 函数...")
        result = add_podcast_item(
            xml_file_path=xml_file_path,
            title=title,
            description=description,
            enclosure_url=enclosure_url,
            pub_date=pub_date,
            length=length,
            cover_image_url=cover_image_url
        )
        
        print(f"\n📥 函数返回结果: {result}")
        
        if result:
            print("✅ add_podcast_item 函数执行成功!")
            
            # 检查生成的文件
            if os.path.exists(xml_file_path):
                print(f"✅ XML 文件已生成: {xml_file_path}")
                
                # 读取并显示文件内容
                with open(xml_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"📄 文件大小: {len(content)} 字符")
                print(f"📄 文件内容预览:")
                print(content[:500] + "..." if len(content) > 500 else content)
                
                # 检查是否包含封面图标签
                if cover_image_url and f'<itunes:image href="{cover_image_url}"' in content:
                    print("✅ 封面图标签已正确添加")
                elif cover_image_url:
                    print("⚠️ 封面图标签未找到")
                
            else:
                print("❌ XML 文件未生成")
                return False
        else:
            print("❌ add_podcast_item 函数执行失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 函数调用异常: {e}")
        import traceback
        print(f"❌ 详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🔧 add_podcast_item 函数测试工具")
    print("=" * 60)
    
    success = test_add_podcast_item()
    
    if success:
        print("\n🎉 测试成功!")
        print("✅ add_podcast_item 函数工作正常")
        print("✅ 封面图功能正常")
        print("💡 问题可能出在 API 接口的其他部分")
    else:
        print("\n❌ 测试失败")
        print("💡 问题出在 add_podcast_item 函数内部")
