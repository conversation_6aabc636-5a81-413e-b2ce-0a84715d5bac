#!/usr/bin/env python3
"""
测试封面图位置（在 enclosure 下面）
"""

import requests
import json
from datetime import datetime

def test_cover_position():
    """测试封面图在 enclosure 下面的位置"""
    print("📸 测试封面图位置（在 enclosure 下面）")
    print("=" * 60)
    
    # 生成当前时间
    pub_date = datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")
    
    payload = {
        "title": "测试封面图位置",
        "description": "测试封面图是否正确放置在 enclosure 标签下面",
        "enclosure_url": "https://example.com/test-position.mp3",
        "cover_image_url": "https://example.com/test-cover-position.jpg",
        "pub_date": pub_date,
        "length": "5000000"
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   音频URL: {payload['enclosure_url']}")
    print(f"   封面图URL: {payload['cover_image_url']}")
    print(f"   发布时间: {payload['pub_date']}")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=90
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("🎉 成功创建 Podcast 条目！")
            print(f"   消息: {result['message']}")
            print(f"   标题: {result['title']}")
            print(f"   XML文件: {result['xml_file']}")
            
            print("\n📋 预期的 XML 结构:")
            print("   <item>")
            print("     <title>测试封面图位置</title>")
            print("     <description>...</description>")
            print("     <enclosure url='...' length='...' type='audio/mpeg'/>")
            print("     <itunes:image href='https://example.com/test-cover-position.jpg'/>")
            print("     <pubDate>...</pubDate>")
            print("     <guid>...</guid>")
            print("   </item>")
            
            return True
        else:
            print("❌ 创建失败")
            try:
                error_info = response.json()
                print(f"   错误: {error_info}")
            except:
                print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_your_real_params():
    """测试您的真实参数"""
    print("\n🎯 测试您的真实参数（封面图在 enclosure 下面）")
    print("=" * 60)
    
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3",
        "cover_image_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送您的真实参数...")
    print(f"   标题: {payload['title']}")
    print(f"   封面图: {payload['cover_image_url']}")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=90
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("🎉 您的真实参数测试成功！")
            print(f"   消息: {result['message']}")
            print(f"   标题: {result['title']}")
            return True
        else:
            print("❌ 真实参数测试失败")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

if __name__ == "__main__":
    print("📸 封面图位置测试工具")
    print("=" * 70)
    
    # 检查服务器状态
    if not check_server_status():
        print("请确保服务器运行在 http://127.0.0.1:7899")
        exit(1)
    
    # 测试1：简单的位置测试
    success1 = test_cover_position()
    
    # 测试2：您的真实参数
    success2 = test_your_real_params()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    print(f"   封面图位置测试: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   真实参数测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("✅ 封面图现在正确放置在 enclosure 下面")
        print("✅ 您的真实参数工作正常")
        print("\n📋 XML 结构:")
        print("   <enclosure url='...' length='...' type='audio/mpeg'/>")
        print("   <itunes:image href='您的封面图URL'/>")
        print("   <pubDate>...</pubDate>")
        print("\n🔗 请检查 GitHub 仓库确认更新:")
        print("   https://github.com/fjxc2021/podcast-feed")
    else:
        print("\n❌ 部分测试失败，请检查服务器日志")
