#!/usr/bin/env python3
"""
重置 Podcast XML 文件
"""

import requests
import json

def reset_podcast_xml():
    """重置 podcast XML 文件"""
    print("🔄 重置 Podcast XML 文件")
    print("=" * 40)
    
    url = "http://127.0.0.1:7899/reset_podcast_xml"
    
    try:
        print("⏳ 正在重置 XML 文件...")
        response = requests.post(url, json={}, timeout=120)
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("🎉 成功重置 Podcast XML 文件！")
            print(f"   消息: {result['message']}")
            print(f"   XML文件: {result['xml_file']}")
            print(f"   仓库: {result['repository']}")
            print("\n✅ XML 文件已重置为干净状态")
            print("✅ 所有测试条目已清除")
            print("✅ 更改已推送到 GitHub")
            print("\n🔗 请检查 GitHub 仓库:")
            print("   https://github.com/fjxc2021/podcast-feed")
            return True
        else:
            print("❌ 重置失败")
            try:
                error_info = response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print("❌ 服务器状态异常")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

if __name__ == "__main__":
    print("🧹 Podcast XML 重置工具")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server_status():
        print("请确保服务器运行在 http://127.0.0.1:7899")
        exit(1)
    
    # 确认操作
    print("\n⚠️  警告: 此操作将:")
    print("   - 清除所有现有的 podcast 条目")
    print("   - 重置 XML 文件为干净的模板状态")
    print("   - 推送更改到 GitHub 仓库")
    
    confirm = input("\n确认要继续吗？(y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        success = reset_podcast_xml()
        
        if success:
            print("\n🎉 重置完成！")
            print("现在可以开始添加正式的 podcast 条目了")
        else:
            print("\n❌ 重置失败，请检查服务器日志")
    else:
        print("\n❌ 操作已取消")
