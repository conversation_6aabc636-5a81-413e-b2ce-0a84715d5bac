#!/usr/bin/env python3
"""
测试 Git SSH 配置
"""

import os
import subprocess

def check_ssh_key():
    """检查 SSH 密钥是否存在"""
    user_home = os.path.expanduser("~")
    ssh_dir = os.path.join(user_home, ".ssh")
    
    print(f"🏠 用户目录: {user_home}")
    print(f"🔑 SSH 目录: {ssh_dir}")
    
    if not os.path.exists(ssh_dir):
        print("❌ SSH 目录不存在")
        return False
    
    # 检查常见的密钥文件
    key_files = ["id_rsa", "id_ed25519", "id_ecdsa"]
    found_keys = []
    
    for key_file in key_files:
        key_path = os.path.join(ssh_dir, key_file)
        if os.path.exists(key_path):
            found_keys.append(key_path)
            print(f"✅ 找到密钥: {key_path}")
    
    if not found_keys:
        print("❌ 未找到 SSH 密钥文件")
        return False
    
    return True

def test_ssh_connection():
    """测试 SSH 连接到 GitHub"""
    print("\n🔍 测试 SSH 连接到 GitHub...")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        user_home = os.path.expanduser("~")
        env["HOME"] = user_home
        
        # Windows 特殊处理
        if os.name == 'nt':
            ssh_key_path = os.path.join(user_home, ".ssh", "id_rsa")
            if os.path.exists(ssh_key_path):
                env["GIT_SSH_COMMAND"] = f'ssh -i "{ssh_key_path}" -o StrictHostKeyChecking=no'
                print(f"🔑 使用密钥: {ssh_key_path}")
        
        result = subprocess.run(
            ["ssh", "-T", "**************"],
            capture_output=True,
            text=True,
            timeout=15,
            env=env
        )
        
        print(f"📤 命令: ssh -T **************")
        print(f"📥 返回码: {result.returncode}")
        print(f"📥 标准输出: {result.stdout}")
        print(f"📥 错误输出: {result.stderr}")
        
        # GitHub SSH 测试通常返回 1，但会有成功消息
        if "successfully authenticated" in result.stderr:
            print("✅ SSH 连接成功！")
            return True
        else:
            print("❌ SSH 连接失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ SSH 连接超时")
        return False
    except Exception as e:
        print(f"❌ SSH 测试异常: {e}")
        return False

def test_git_clone():
    """测试 Git 克隆操作"""
    print("\n🧪 测试 Git 克隆操作...")
    
    test_repo = "**************:fjxc2021/podcast-feed.git"
    test_dir = r"D:\temp\test-podcast-clone"
    
    # 如果目录存在，先删除
    if os.path.exists(test_dir):
        import shutil
        shutil.rmtree(test_dir)
        print(f"🗑️ 删除现有目录: {test_dir}")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        user_home = os.path.expanduser("~")
        env["HOME"] = user_home
        
        if os.name == 'nt':
            ssh_key_path = os.path.join(user_home, ".ssh", "id_rsa")
            if os.path.exists(ssh_key_path):
                env["GIT_SSH_COMMAND"] = f'ssh -i "{ssh_key_path}" -o StrictHostKeyChecking=no'
        
        # 确保父目录存在
        os.makedirs(os.path.dirname(test_dir), exist_ok=True)
        
        print(f"📥 克隆仓库: {test_repo}")
        print(f"📁 目标目录: {test_dir}")
        
        result = subprocess.run(
            ["git", "clone", test_repo, test_dir],
            capture_output=True,
            text=True,
            timeout=30,
            env=env
        )
        
        print(f"📥 返回码: {result.returncode}")
        print(f"📥 标准输出: {result.stdout}")
        print(f"📥 错误输出: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ Git 克隆成功！")
            
            # 检查克隆的内容
            if os.path.exists(test_dir):
                files = os.listdir(test_dir)
                print(f"📂 克隆的文件: {files}")
            
            return True
        else:
            print("❌ Git 克隆失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Git 克隆超时")
        return False
    except Exception as e:
        print(f"❌ Git 克隆异常: {e}")
        return False

def main():
    print("🔧 Git SSH 配置测试")
    print("=" * 50)
    
    # 1. 检查 SSH 密钥
    if not check_ssh_key():
        print("\n❌ SSH 密钥检查失败，请先配置 SSH 密钥")
        return
    
    # 2. 测试 SSH 连接
    if not test_ssh_connection():
        print("\n❌ SSH 连接测试失败")
        print("💡 建议:")
        print("   1. 检查 SSH 密钥是否已添加到 GitHub")
        print("   2. 运行: ssh-add ~/.ssh/id_rsa")
        print("   3. 检查网络连接")
        return
    
    # 3. 测试 Git 克隆
    if not test_git_clone():
        print("\n❌ Git 克隆测试失败")
        print("💡 建议:")
        print("   1. 检查仓库是否存在")
        print("   2. 检查仓库访问权限")
        return
    
    print("\n🎉 所有测试通过！Git SSH 配置正常")
    print("✅ 现在可以正常使用 Podcast API 了")

if __name__ == "__main__":
    main()
