#!/usr/bin/env python3
"""
测试 Podcast API 的封面图功能
"""

import requests
import json
from datetime import datetime

def test_podcast_with_cover():
    """测试带封面图的 podcast 条目"""
    print("🎙️ 测试带封面图的 Podcast 条目")
    print("=" * 50)
    
    # 生成当前时间的 RFC 2822 格式
    pub_date = datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")
    
    payload = {
        "title": "第1期：AI 让你不焦虑（带封面图）",
        "description": "本期内容讲述AI如何帮助内容创作者减压，现在包含精美封面图",
        "enclosure_url": "https://yourdomain.com/audio/ep1-with-cover.mp3",
        "pub_date": pub_date,
        "length": "15678900",
        "cover_image_url": "https://yourdomain.com/images/ep1-cover.jpg"  # 新增封面图参数
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   描述: {payload['description']}")
    print(f"   音频URL: {payload['enclosure_url']}")
    print(f"   封面图URL: {payload['cover_image_url']}")
    print(f"   发布时间: {payload['pub_date']}")
    
    try:
        print("\n⏳ 正在处理请求...")
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=120
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("🎉 成功创建带封面图的 Podcast 条目！")
            print(f"   消息: {result['message']}")
            print(f"   标题: {result['title']}")
            print(f"   XML文件: {result['xml_file']}")
            print(f"   仓库: {result['repository']}")
            return True
        else:
            print("❌ 创建失败")
            try:
                error_info = response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_podcast_without_cover():
    """测试不带封面图的 podcast 条目（向后兼容）"""
    print("\n🎙️ 测试不带封面图的 Podcast 条目（向后兼容）")
    print("=" * 50)
    
    # 生成当前时间的 RFC 2822 格式
    pub_date = datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")
    
    payload = {
        "title": "第2期：深度学习入门（无封面图）",
        "description": "从零开始学习深度学习的基础概念和实践方法",
        "enclosure_url": "https://yourdomain.com/audio/ep2-no-cover.mp3",
        "pub_date": pub_date,
        "length": "18000000"
        # 注意：这里没有 cover_image_url 参数
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   描述: {payload['description']}")
    print(f"   音频URL: {payload['enclosure_url']}")
    print(f"   封面图URL: 无（测试向后兼容）")
    print(f"   发布时间: {payload['pub_date']}")
    
    try:
        print("\n⏳ 正在处理请求...")
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=120
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("🎉 成功创建不带封面图的 Podcast 条目！")
            print(f"   消息: {result['message']}")
            print(f"   标题: {result['title']}")
            print(f"   XML文件: {result['xml_file']}")
            print(f"   仓库: {result['repository']}")
            return True
        else:
            print("❌ 创建失败")
            try:
                error_info = response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print("❌ 服务器状态异常")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

if __name__ == "__main__":
    print("📸 Podcast 封面图功能测试")
    print("=" * 60)
    
    # 检查服务器状态
    if not check_server_status():
        print("请确保服务器运行在 http://127.0.0.1:7899")
        exit(1)
    
    print("\n🧪 开始测试封面图功能...")
    
    # 测试1：带封面图的条目
    success1 = test_podcast_with_cover()
    
    # 测试2：不带封面图的条目（向后兼容）
    success2 = test_podcast_without_cover()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   带封面图的条目: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   不带封面图的条目: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！封面图功能正常工作")
        print("\n📋 生成的 XML 结构:")
        print("   - 带封面图的条目包含 <itunes:image href='...'/>")
        print("   - 不带封面图的条目保持原有格式")
        print("\n🔗 请检查 GitHub 仓库确认更新:")
        print("   https://github.com/fjxc2021/podcast-feed")
    else:
        print("\n❌ 部分测试失败，请检查服务器日志")
    
    print("\n💡 使用说明:")
    print("   - cover_image_url 是可选参数")
    print("   - 支持 JPG、PNG 等常见图片格式")
    print("   - 建议图片尺寸: 1400x1400 或 3000x3000 像素")
    print("   - 图片应该是正方形，用于播客应用显示")
