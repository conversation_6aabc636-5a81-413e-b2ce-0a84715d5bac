#!/usr/bin/env python3
"""
测试完整参数（包含封面图）
"""

import requests
import json

def test_full_params():
    """测试您提供的完整参数"""
    print("🎯 测试完整参数（包含封面图）")
    
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3",
        "cover_image_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送请求...")
    print("   标题:", payload["title"])
    print("   封面图:", payload["cover_image_url"])
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=60
        )
        
        print(f"\n📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 成功创建!")
            print("   标题:", result["title"])
            print("   消息:", result["message"])
            print("   XML文件:", result["xml_file"])
            return True
        else:
            print("❌ 创建失败")
            print("   响应:", response.text)
            return False
            
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return False

if __name__ == "__main__":
    success = test_full_params()
    
    if success:
        print("\n🎉 完整参数测试成功!")
        print("✅ 您的 Podcast 条目已创建")
        print("📋 注意: 封面图参数已接收但暂时未处理")
    else:
        print("\n❌ 测试失败")
