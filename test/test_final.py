#!/usr/bin/env python3
"""
最终测试 - 您的完整参数
"""

import requests
import json

def test_your_params():
    """测试您的完整参数"""
    print("🎯 测试您的完整参数")
    print("=" * 50)
    
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3",
        "cover_image_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送请求...")
    print("   标题:", payload["title"])
    print("   封面图:", payload["cover_image_url"])
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=90
        )
        
        print(f"\n📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 成功!")
            print("   消息:", result["message"])
            print("   标题:", result["title"])
            print("   XML文件:", result["xml_file"])
            print("\n📋 注意: 封面图参数已接收但暂时未处理")
            return True
        else:
            print("❌ 失败")
            print("   响应:", response.text)
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

if __name__ == "__main__":
    # 检查服务器
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器状态异常")
            exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        exit(1)
    
    success = test_your_params()
    
    if success:
        print("\n🎉 您的参数测试成功!")
        print("✅ Podcast 条目已创建并推送到 GitHub")
        print("📋 下一步: 实现封面图功能")
    else:
        print("\n❌ 测试失败，需要进一步调试")
