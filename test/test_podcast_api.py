#!/usr/bin/env python3
"""
测试 Podcast API 接口
"""

import requests
import json
from datetime import datetime

# 服务器配置
BASE_URL = "http://127.0.0.1:7899"

def test_create_podcast_item():
    """测试创建 podcast 条目"""
    print("🎙️ 测试创建 Podcast 条目...")
    url = f"{BASE_URL}/create_podcast_item"
    
    # 生成当前时间的 RFC 2822 格式
    current_time = datetime.now()
    pub_date = current_time.strftime("%a, %d %b %Y %H:%M:%S +0800")
    
    payload = {
        "title": "第1期：AI 让你不焦虑",
        "description": "本期内容讲述AI如何帮助内容创作者减压",
        "enclosure_url": "https://yourdomain.com/audio/ep1.mp3",
        "pub_date": pub_date,
        "length": "12345678"
    }
    
    try:
        print(f"📤 发送请求...")
        print(f"   标题: {payload['title']}")
        print(f"   描述: {payload['description']}")
        print(f"   音频URL: {payload['enclosure_url']}")
        print(f"   发布时间: {payload['pub_date']}")
        
        response = requests.post(url, json=payload, timeout=60)
        print(f"\n📥 响应状态码: {response.status_code}")
        print(f"📥 响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            print("✅ Podcast 条目创建成功！")
            return True
        else:
            print("❌ Podcast 条目创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_create_multiple_episodes():
    """测试创建多个 podcast 条目"""
    print("\n🎙️ 测试创建多个 Podcast 条目...")
    
    episodes = [
        {
            "title": "第2期：深度学习入门指南",
            "description": "从零开始学习深度学习的基础概念和实践方法",
            "enclosure_url": "https://yourdomain.com/audio/ep2.mp3"
        },
        {
            "title": "第3期：开源项目贡献指南",
            "description": "如何参与开源项目，从新手到贡献者的完整路径",
            "enclosure_url": "https://yourdomain.com/audio/ep3.mp3"
        },
        {
            "title": "第4期：远程工作的艺术",
            "description": "分享远程工作的技巧、工具和最佳实践",
            "enclosure_url": "https://yourdomain.com/audio/ep4.mp3"
        }
    ]
    
    url = f"{BASE_URL}/create_podcast_item"
    successful_count = 0
    
    for i, episode in enumerate(episodes, 1):
        print(f"\n📻 创建第 {i} 个条目...")
        
        # 生成时间（每个条目间隔1小时）
        current_time = datetime.now()
        current_time = current_time.replace(hour=current_time.hour + i)
        pub_date = current_time.strftime("%a, %d %b %Y %H:%M:%S +0800")
        
        payload = {
            "title": episode["title"],
            "description": episode["description"],
            "enclosure_url": episode["enclosure_url"],
            "pub_date": pub_date,
            "length": str(12345678 + i * 1000000)  # 不同的文件大小
        }
        
        try:
            response = requests.post(url, json=payload, timeout=60)
            if response.status_code == 200:
                print(f"   ✅ 成功: {episode['title']}")
                successful_count += 1
            else:
                print(f"   ❌ 失败: {episode['title']} - {response.json()}")
        except Exception as e:
            print(f"   ❌ 异常: {episode['title']} - {e}")
    
    print(f"\n📊 批量创建结果: {successful_count}/{len(episodes)} 成功")
    return successful_count == len(episodes)

def test_error_cases():
    """测试错误情况"""
    print("\n🚫 测试错误情况...")
    url = f"{BASE_URL}/create_podcast_item"
    
    # 测试缺少必需参数
    test_cases = [
        {
            "name": "缺少 title",
            "payload": {
                "description": "测试描述",
                "enclosure_url": "https://test.com/audio.mp3",
                "pub_date": "Mon, 01 Jul 2025 12:00:00 +0800"
            }
        },
        {
            "name": "缺少 description", 
            "payload": {
                "title": "测试标题",
                "enclosure_url": "https://test.com/audio.mp3",
                "pub_date": "Mon, 01 Jul 2025 12:00:00 +0800"
            }
        },
        {
            "name": "缺少 enclosure_url",
            "payload": {
                "title": "测试标题",
                "description": "测试描述",
                "pub_date": "Mon, 01 Jul 2025 12:00:00 +0800"
            }
        },
        {
            "name": "缺少 pub_date",
            "payload": {
                "title": "测试标题",
                "description": "测试描述",
                "enclosure_url": "https://test.com/audio.mp3"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 测试: {test_case['name']}")
        try:
            response = requests.post(url, json=test_case['payload'], timeout=30)
            if response.status_code == 400:
                print(f"   ✅ 正确返回错误: {response.json()}")
            else:
                print(f"   ❌ 未正确处理错误: {response.status_code} - {response.json()}")
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        # 使用一个简单的接口来检查服务器是否运行
        response = requests.get(f"{BASE_URL}/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print(f"   请确保服务器运行在 {BASE_URL}")
        return False

if __name__ == "__main__":
    print("🎙️ Podcast API 测试工具")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server_status():
        exit(1)
    
    print("\n选择测试类型:")
    print("1. 创建单个 Podcast 条目")
    print("2. 创建多个 Podcast 条目")
    print("3. 测试错误情况")
    print("4. 运行所有测试")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        test_create_podcast_item()
    elif choice == "2":
        test_create_multiple_episodes()
    elif choice == "3":
        test_error_cases()
    elif choice == "4":
        print("\n🧪 运行所有测试...")
        test_create_podcast_item()
        test_create_multiple_episodes()
        test_error_cases()
    else:
        print("无效选择，运行单个条目测试...")
        test_create_podcast_item()
    
    print("\n🎉 测试完成！")
    print("\n💡 提示:")
    print("   - 检查 GitHub 仓库是否已更新")
    print("   - 验证 podcast.xml 文件内容")
    print("   - 确认 Git 提交记录")
