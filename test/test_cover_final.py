#!/usr/bin/env python3
"""
测试最终的封面图功能
"""

import requests
import json

def test_your_params():
    """测试您提供的完整参数"""
    print("🎯 测试您的完整参数（包含封面图）")
    print("=" * 60)
    
    # 您提供的确切参数
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3",
        "cover_image_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   音频URL: {payload['enclosure_url']}")
    print(f"   封面图URL: {payload['cover_image_url']}")
    print(f"   发布时间: {payload['pub_date']}")
    
    try:
        print("\n⏳ 正在处理请求...")
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=120
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("🎉 成功创建 Podcast 条目！")
            print(f"   状态: {result.get('status', 'N/A')}")
            print(f"   消息: {result.get('message', 'N/A')}")
            print(f"   标题: {result.get('title', 'N/A')}")
            print(f"   XML文件: {result.get('xml_file', 'N/A')}")
            print(f"   仓库: {result.get('repository', 'N/A')}")
            
            print("\n✅ 预期的 XML 结构:")
            print("   <item>")
            print("     <title>Nvidia Drops a Code AI Bomb...</title>")
            print("     <description>NVIDIA just open-sourced OCR...</description>")
            print("     <enclosure url='...' length='...' type='audio/mpeg'/>")
            print("     <itunes:image href='https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/.../cover.png'/>")
            print("     <pubDate>Fri, 09 May 2025 07:34:01 GMT</pubDate>")
            print("     <guid isPermaLink='true'>...</guid>")
            print("   </item>")
            
            return True
        else:
            print("❌ 创建失败")
            try:
                error_info = response.json()
                print(f"   错误: {json.dumps(error_info, indent=2, ensure_ascii=False)}")
            except:
                print(f"   响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_without_cover():
    """测试不带封面图的情况（向后兼容）"""
    print("\n🔍 对比测试：不带封面图")
    print("=" * 60)
    
    payload = {
        "title": "Test Without Cover Image",
        "description": "This is a test without cover image to ensure backward compatibility",
        "enclosure_url": "https://example.com/test-no-cover.mp3",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送请求（不带封面图）...")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=60
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 不带封面图的测试成功")
            print(f"   消息: {result.get('message', 'N/A')}")
            return True
        else:
            print("❌ 不带封面图的测试失败")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

if __name__ == "__main__":
    print("🎙️ 最终封面图功能测试")
    print("=" * 70)
    
    # 检查服务器状态
    if not check_server_status():
        print("请确保服务器运行在 http://127.0.0.1:7899")
        exit(1)
    
    # 测试您的完整参数
    success1 = test_your_params()
    
    # 测试向后兼容性
    success2 = test_without_cover()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    print(f"   带封面图的完整参数: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   不带封面图的兼容性: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("✅ 封面图功能正常工作")
        print("✅ 向后兼容性良好")
        print("✅ 您的 Podcast 条目已创建并推送到 GitHub")
        print("\n📋 后续步骤:")
        print("   1. 检查 GitHub 仓库: https://github.com/fjxc2021/podcast-feed")
        print("   2. 验证 podcast.xml 文件包含 <itunes:image> 标签")
        print("   3. 确认封面图 URL 正确显示")
        print("\n💡 XML 结构说明:")
        print("   - 封面图使用 <itunes:image href='URL'/> 标签")
        print("   - 位置在 enclosure 和 pubDate 之间")
        print("   - 兼容 iTunes 和其他播客应用")
    elif success1:
        print("\n🎉 主要功能正常！")
        print("✅ 您的参数测试成功")
        print("⚠️ 向后兼容性测试失败")
    elif success2:
        print("\n⚠️ 基础功能正常，但封面图有问题")
        print("✅ 向后兼容性良好")
        print("❌ 封面图功能需要调试")
    else:
        print("\n❌ 所有测试都失败")
        print("💡 建议检查服务器日志获取详细错误信息")
