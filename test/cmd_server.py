import base64

from flask import Flask, request, jsonify
import subprocess
import shlex
import json
import os
from multipart import file_path

import random
import time
from pydub import AudioSegment
import re
import tempfile
import requests
import boto3
from datetime import datetime
import xml.etree.ElementTree as ET
from xml.dom import minidom
import shutil

app = Flask(__name__)

# 全局变量用于跟踪 SD WebUI 进程状态
sd_webui_process = None
sd_webui_status = "stopped"  # stopped, starting, running, stopping

# Windows 上你的 .bat 脚本路径
BATCH_PATH = r"D:\tools\tts_run.bat"
# 本地SD Web UI API地址
SD_API_URL = "http://127.0.0.1:7860/sdapi/v1/txt2img"
@app.route('/run', methods=['POST'])
def run_command():
    env_path = r"D:\software\anaconda\Scripts\activate.bat"
    conda_env = "agent_cline"
    python_path = r"D:\software\anaconda\envs\agent_cline\python.exe"
    # file_paths = r"D:\n8n-docker\files"
    try:
        #raw_data = request.get_data(as_text=True)
        raw_data = request.get_json()
        print(type(raw_data))
        print(raw_data)
        # data = json.loads(raw_data) if isinstance(raw_data, str) else {}
        file =  raw_data["file"]
        voice = raw_data["voice"]
        # title = file.rsplit(".txt", 1)[0]
        title = os.path.splitext(file)[0]
        file_path = rf"D:\n8n-docker\files\{file}"
    except Exception as e:
        return jsonify({"error": "Invalid JSON format", "detail": str(e)}), 400
    mp3_file = rf"D:\n8n-docker\files\{title}.mp3"
    os.makedirs(os.path.dirname(mp3_file), exist_ok=True)
    # cmd = f'''cmd.exe /c "call {env_path} {conda_env} && edge-tts --file \"{file_path}\" --voice zh-CN-YunxiNeural --write-media \"{mp3_file}\""'''
    cmd = [
        python_path, "-m", "edge_tts",
        "--file", file_path,
        # "--voice", "zh-CN-YunxiNeural",
        "--voice", voice,
        "--rate", "+12%",
        "--write-media", mp3_file
    ]
    try:
        print(cmd)
        result = subprocess.run(cmd, shell=True, capture_output=True, check=True)
        print("标准输出:", result.stdout.decode('utf-8', errors='ignore'))
        print("标准错误:", result.stderr.decode('utf-8', errors='ignore'))
        print("退出码:", result.returncode)
        return jsonify({
            "stdout": result.stdout.decode('utf-8', errors='ignore'),
            "stderr": result.stderr.decode('utf-8', errors='ignore'),
            "returncode": result.returncode
        })
    except Exception as e:
        error_detail = {
            "error": str(e),
            "cmd": cmd,
            "stdout": result.stdout.decode('utf-8', errors='ignore') if 'result' in locals() else "",
            "stderr": result.stderr.decode('utf-8', errors='ignore') if 'result' in locals() else ""
        }
        print("调试信息:", error_detail)  # 添加调试信息
        return jsonify(error_detail), 500


# ========= 新增：开关参数 =========
USE_ONLINE_DOWNLOAD = False  # True: 在线下载音乐；False: 本地随机选取音乐

# ========= 用户参数 ==========
CLIENT_ID = '8ccdf49e'
DOWNLOAD_DIR = 'D:\data\mp3'
FFMPEG_PATH = "ffmpeg"  # 若 pydub 无法找到ffmpeg可指定全路径

# 你要合成的新闻正文 mp3 路径
# NEWS_MP3_PATH = r'D:\n8n-docker\files\开源_AI_爆冷_英伟达_OCR_模型_颠覆你的代码开发_.mp3'

NEWS_OUT_DIR = r'D:\n8n-docker\files'


SEARCH_CONFIG = {
    'intro':     ['intro jingle opener ident logo', '', 3, 15, 1],    # intro曲，3-15秒
    'outro':     ['outro ending close stinger finale', '', 3, 15, 1],    # outro曲，3-15秒
    'bgm':       ['background corporate ambient uplifting','', 30, 180, 1],  # 背景曲
}
# 每种类型实际裁剪目标（单位：ms）
TRIM_TARGETS = {
    'intro': 9000,   # 裁剪前9秒
    'outro': 20000,  # 裁剪最后20秒
    'bgm': None      # bgm保持原始长度，可自定义截断或循环
}

# stable-diffusion-webui 相关配置
SD_WEBUI_DIR = '/mnt/d/sd_data/stable-diffusion-webui'
CONDA_PATH = '/home/<USER>/miniconda3/etc/profile.d/conda.sh'
CONDA_ENV_NAME = 'sdxl'
PYTHON_CMD = 'python'
WEBUI_PORT = 7860
# OUTPUT_DIR = os.path.join(SD_WEBUI_DIR, 'outputs', 'txt2img-images')
OUTPUT_DIR = r'D:\n8n-docker\files'
# D:\n8n-docker\files\

# Podcast 相关配置
GITHUB_REPO_URL = "**************:fjxc2021/podcast-feed.git"
PODCAST_REPO_DIR = r'D:\temp\podcast-feed'
PODCAST_XML_FILE = "podcast.xml"

# ===== 工具函数 =====
def build_query_params(query, genre, dur_min, dur_max, limit):
    return {
        'client_id': CLIENT_ID,
        'format': 'json',
        'audioformat': 'mp32',
        'search': query,
        'limit': limit,
        'durationmin': dur_min,
        'durationmax': dur_max,
        'fuzzytags': genre,
        'order': 'popularity_total',
        'include_licenses': 'yes',
        'offset': random.randint(0, 20)  # 增加每次查询的变化
    }

def download_mp3(url, path):
    try:
        with requests.get(url, stream=True, timeout=20) as r:
            r.raise_for_status()
            with open(path, 'wb') as f:
                for chunk in r.iter_content(8192):
                    f.write(chunk)
        # 检查文件是否真的下载下来了
        if os.path.exists(path) and os.path.getsize(path) > 1024:
            return True
    except Exception as e:
        print(f"⚠️ 下载失败: {url} → {e}")
        return False

def auto_trim_audio(file_path, out_path, trim_type, ms_target):
    if not os.path.exists(file_path):
        print(f"⚠️ 找不到要裁剪的音频文件: {file_path}")
        return False
    try:
        audio = AudioSegment.from_file(file_path)
        orig_len = len(audio)
        if trim_type == 'intro':
            clip = audio[:ms_target]
        elif trim_type == 'outro':
            clip = audio[-ms_target:]
        else:
            clip = audio
        # 强制后缀.mp3
        if not out_path.lower().endswith(".mp3"):
            out_path += ".mp3"
        print("out_path--", out_path)
        clip.export(out_path, format="mp3")
        print(f"    ✂️ 裁剪完成: {out_path} | 原始{orig_len//1000}s → 裁剪{len(clip)//1000}s")
        return True
    except Exception as e:
        print(f"⚠️ 裁剪失败: {file_path} → {e}")
        return False

def fetch_and_save(category, conf, trim_ms):
    query, genre, dur_min, dur_max, limit = conf
    save_dir = os.path.join(DOWNLOAD_DIR, category)
    os.makedirs(save_dir, exist_ok=True)
    url = 'https://api.jamendo.com/v3.0/tracks'
    params = build_query_params(query, genre, dur_min, dur_max,limit)
    print(f"🔎 [{category}] 查询: {query}, 风格: {genre}, 时长: {dur_min}-{dur_max}秒 ...[offset={params['offset']}]")
    try:
        resp = requests.get(url, params=params, timeout=30)
        if resp.status_code != 200:
            print(f"❌ API请求失败({resp.status_code}): {resp.text}")
            return
        results = resp.json().get('results', [])
        if not results:
            print(f"❗ 未找到任何音乐, 请更换关键词或放宽条件")
            return

        for idx, track in enumerate(results):
            # 拼接保存文件名
            name = f"{category}_{track['id']}_{track['name'].replace(' ','_')[:20]}"
            mp3_url = track['audio']
            safe_name = re.sub(r'[\\/*?:"<>|&=]', '_', name)
            raw_fname = os.path.join(save_dir, f"{safe_name}_raw.mp3")
            # ext = mp3_url.split('.')[-1]
            # raw_fname = os.path.join(save_dir, f"{name}_raw.{ext}")
            out_fname = os.path.join(save_dir, f"{name}_trimmed.{safe_name}")
            print(f"  ⬇️ 下载: {raw_fname}")
            if os.path.exists(out_fname):
                print(f"⚠️ 文件已存在，跳过: {out_fname}")
                continue
            if download_mp3(mp3_url, raw_fname):
                # print(f"    ✅ 成功: {raw_fname} | [{track['duration']}秒] | 风格: {track['musicinfo']['tags']}")
                tags = track.get('musicinfo', {}).get('tags', [])
                # print(f"    ✅ 成功: {raw_fname} | [{track['duration']}秒] | 风格: {tags}")
                print(f"    ✅ 成功: {raw_fname} | [{track.get('duration', '?')}秒] | 风格: {tags or '无'}")
                # 裁剪处理
                if trim_ms:
                    if auto_trim_audio(raw_fname, out_fname, category, trim_ms):
                        os.remove(raw_fname)
                    else:
                        print(f"    ⚠️ 裁剪失败, 保留原始文件: {raw_fname}")
                else:
                    # BGM原样重命名为_trimmed
                    if not out_fname.lower().endswith(".mp3"):
                        out_fname += ".mp3"
                    if os.path.exists(out_fname):  # 关键加这一行
                        os.remove(out_fname)
                    os.rename(raw_fname, out_fname)
            else:
                print(f"    ❌ 失败: {raw_fname}")
            time.sleep(1)
    except Exception as e:
        print(f"❌ [{category}] 检索异常: {e}")


def get_latest_mp3(dir_path):
    trimmed = [os.path.join(dir_path, f) for f in os.listdir(dir_path) if f.endswith('.mp3')]
    if not trimmed:
        return None
    return max(trimmed, key=os.path.getmtime)

# 获取本地随机音乐
def get_random_mp3(dir_path):
    files = [os.path.join(dir_path, f) for f in os.listdir(dir_path) if f.endswith('.mp3')]
    if not files:
        return None
    return random.choice(files)

def extract_track_id(path):
    m = re.search(r'_(\d+)_', os.path.basename(path))
    return m.group(1) if m else path



def auto_mix(news_mp3_path, intro_path, outro_path, bgm_path, out_dir):
    news_base = os.path.basename(news_mp3_path)
    prefix = os.path.splitext(news_base)[0]
    output_path = os.path.join(out_dir, f"{prefix}_final_mix.mp3")

    # 读音频
    intro = AudioSegment.from_file(intro_path)
    outro = AudioSegment.from_file(outro_path)
    bgm = AudioSegment.from_file(bgm_path)
    news = AudioSegment.from_file(news_mp3_path)

    # 淡入淡出参数
    intro_fade_out = 2000    # ms
    outro_fade_in = 2000     # ms
    news_fade_in = 1500
    news_fade_out = 1000
    bgm_volume = -15      # dB（比之前再低一点）背景音量

    # 处理各段
    intro = intro.fade_out(intro_fade_out)
    news = news.fade_in(news_fade_in).fade_out(news_fade_out)
    outro = outro.fade_in(outro_fade_in)

    # 组合主音轨
    main_track = intro + news + outro

    # 背景音乐只铺“正文+结尾”，不铺intro
    bgm = bgm - abs(bgm_volume)


    if len(bgm) < len(news):
        loop_times = (len(news) // len(bgm)) + 1
        bgm = (bgm * loop_times)[:len(news)]
    else:
        bgm = bgm[:len(news)]
    # bgm 段加淡入淡出
    bgm = bgm.fade_in(2000).fade_out(2000)

    # 合成部分：outro后不再叠加bgm
    news_with_bgm = news.overlay(bgm)
    final_audio = intro + news_with_bgm + outro



    final_audio.export(output_path, format="mp3")
    print(f"✅ 合成完成: {output_path}")
    return output_path


def temp_trim_audio(src_path, trim_type, ms_target):
    """临时裁剪一份音频用于合成，不改动原始文件。"""
    if ms_target is None:
        return src_path  # 背景音乐无需裁剪
    try:
        audio = AudioSegment.from_file(src_path)
        orig_len = len(audio)
        if trim_type == 'intro':
            if orig_len < ms_target:
                print(f"⚠️ 开场音乐时长不足，原始{orig_len//1000}s，目标{ms_target//1000}s")
            clip = audio[:ms_target]
        elif trim_type == 'outro':
            if orig_len < ms_target:
                print(f"⚠️ 结尾音乐时长不足，原始{orig_len//1000}s，目标{ms_target//1000}s")
            clip = audio[-ms_target:]
        else:
            clip = audio
        # 生成临时文件
        tmp_fd, tmp_path = tempfile.mkstemp(suffix='.mp3', prefix=f'trim_{trim_type}_')
        os.close(tmp_fd)  # 立即关闭描述符，否则win下会锁定文件
        clip.export(tmp_path, format='mp3')
        print(f"✂️ 临时裁剪 {trim_type} 成功：{tmp_path} | 原始{orig_len//1000}s → {len(clip)//1000}s")
        return tmp_path
    except Exception as e:
        print(f"⚠️ 临时裁剪失败: {src_path} → {e}")
        return src_path  # 失败则退回原文件

def is_temp_trim_file(path):
    """判断是否为本次生成的临时裁剪文件"""
    temp_dir = tempfile.gettempdir()
    fname = os.path.basename(path)
    return path.startswith(temp_dir) and fname.startswith('trim_') and fname.endswith('.mp3')


# ===== Podcast XML 处理函数 =====

def create_podcast_xml_template():
    """创建 podcast XML 模板"""
    rss = ET.Element("rss", version="2.0")
    rss.set("xmlns:itunes", "http://www.itunes.com/dtds/podcast-1.0.dtd")

    channel = ET.SubElement(rss, "channel")

    # 基本信息
    ET.SubElement(channel, "title").text = "My Podcast"
    ET.SubElement(channel, "description").text = "A podcast about technology and life."
    ET.SubElement(channel, "link").text = "https://yourdomain.com"
    ET.SubElement(channel, "language").text = "en-US"
    ET.SubElement(channel, "copyright").text = f"© {datetime.now().year} My Podcast"
    ET.SubElement(channel, "lastBuildDate").text = datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")

    # iTunes 特定标签
    ET.SubElement(channel, "itunes:author").text = "Podcast Author"
    ET.SubElement(channel, "itunes:summary").text = "A podcast exploring technology and lifestyle."
    ET.SubElement(channel, "itunes:category", text="Technology")

    return rss


def add_podcast_item(xml_file_path, title, description, enclosure_url, pub_date, length="12345678", cover_image_url=None):
    """向 podcast XML 文件添加新的 item"""
    try:
        # 如果文件不存在，创建新的
        if not os.path.exists(xml_file_path):
            print(f"创建新的 podcast XML 文件: {xml_file_path}")
            rss = create_podcast_xml_template()
        else:
            # 解析现有文件
            tree = ET.parse(xml_file_path)
            rss = tree.getroot()

        # 找到 channel 元素
        channel = rss.find("channel")
        if channel is None:
            raise ValueError("无效的 podcast XML 格式：找不到 channel 元素")

        # 创建新的 item
        item = ET.Element("item")
        ET.SubElement(item, "title").text = title
        ET.SubElement(item, "description").text = description

        # 创建 enclosure 元素
        enclosure = ET.SubElement(item, "enclosure")
        enclosure.set("url", enclosure_url)
        enclosure.set("length", str(length))
        enclosure.set("type", "audio/mpeg")
        ET.SubElement(item, "{http://www.itunes.com/dtds/podcast-1.0.dtd}image").set("href", cover_image_url)

        # if cover_image_url:
        #     image = ET.SubElement(item, "itunes: image")
        #     image.set("href", cover_image_url)

        ET.SubElement(item, "pubDate").text = pub_date

        # 添加 GUID（使用 URL 作为唯一标识）
        guid = ET.SubElement(item, "guid")
        guid.text = enclosure_url
        guid.set("isPermaLink", "true")

        # 将新 item 插入到 channel 的开头（最新的在前面）
        channel.insert(0, item)

        # 更新 lastBuildDate
        last_build_date = channel.find("lastBuildDate")
        if last_build_date is not None:
            last_build_date.text = datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")

        # 格式化并保存 XML
        rough_string = ET.tostring(rss, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")

        # 移除空行
        pretty_xml = '\n'.join([line for line in pretty_xml.split('\n') if line.strip()])

        with open(xml_file_path, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)

        print(f"✅ 成功添加 podcast item: {title}")
        return True

    except Exception as e:
        print(f"❌ 添加 podcast item 失败: {e}")
        return False


def get_git_env():
    """获取 Git 操作所需的环境变量"""
    env = os.environ.copy()

    # 确保 SSH 相关环境变量
    user_home = os.path.expanduser("~")
    ssh_dir = os.path.join(user_home, ".ssh")

    # 设置 SSH 相关环境变量
    env["HOME"] = user_home
    env["SSH_AUTH_SOCK"] = env.get("SSH_AUTH_SOCK", "")

    # 如果是 Windows，可能需要设置 GIT_SSH_COMMAND
    if os.name == 'nt':  # Windows
        # 尝试多种密钥类型
        key_types = ["id_ed25519", "id_rsa", "id_ecdsa"]
        ssh_key_path = None

        for key_type in key_types:
            potential_key = os.path.join(ssh_dir, key_type)
            if os.path.exists(potential_key):
                ssh_key_path = potential_key
                break

        if ssh_key_path:
            env["GIT_SSH_COMMAND"] = f'ssh -i "{ssh_key_path}" -o StrictHostKeyChecking=no'
            print(f"🔑 使用 SSH 密钥: {ssh_key_path}")
        else:
            print(f"⚠️ 未找到 SSH 密钥在: {ssh_dir}")

    return env


def test_git_ssh_connection():
    """测试 SSH 连接到 GitHub"""
    try:
        env = get_git_env()
        result = subprocess.run(
            ["ssh", "-T", "**************"],
            capture_output=True,
            text=True,
            timeout=10,
            env=env
        )
        print(f"🔍 SSH 测试输出: {result.stderr}")
        # GitHub SSH 测试通常返回 1，但会有成功消息
        if "successfully authenticated" in result.stderr:
            print("✅ SSH 连接到 GitHub 成功")
            return True
        else:
            print("❌ SSH 连接到 GitHub 失败")
            return False
    except Exception as e:
        print(f"❌ SSH 测试异常: {e}")
        return False


def git_clone_or_pull(repo_url, local_dir):
    """克隆或拉取 Git 仓库"""
    try:
        # 获取环境变量
        env = get_git_env()

        # 先测试 SSH 连接
        print("🔍 测试 SSH 连接...")
        if not test_git_ssh_connection():
            print("⚠️ SSH 连接测试失败，但继续尝试 Git 操作...")

        if os.path.exists(local_dir):
            # 如果目录存在，尝试拉取最新代码
            print(f"📁 目录已存在，拉取最新代码: {local_dir}")
            result = subprocess.run(
                ["git", "pull"],
                cwd=local_dir,
                capture_output=True,
                text=True,
                check=True,
                env=env
            )
            print(f"✅ Git pull 成功: {result.stdout}")
        else:
            # 克隆仓库
            print(f"📥 克隆仓库: {repo_url} -> {local_dir}")
            # 确保父目录存在
            os.makedirs(os.path.dirname(local_dir), exist_ok=True)
            result = subprocess.run(
                ["git", "clone", repo_url, local_dir],
                capture_output=True,
                text=True,
                check=True,
                env=env
            )
            print(f"✅ Git clone 成功: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Git 操作失败:")
        print(f"   命令: {e.cmd}")
        print(f"   返回码: {e.returncode}")
        print(f"   标准输出: {e.stdout}")
        print(f"   错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Git 操作异常: {e}")
        return False


def git_commit_and_push(local_dir, commit_message):
    """提交并推送更改到 Git 仓库"""
    try:
        # 获取环境变量
        env = get_git_env()

        # 设置 Git 用户信息（如果未设置）
        try:
            subprocess.run(
                ["git", "config", "user.email", "<EMAIL>"],
                cwd=local_dir,
                capture_output=True,
                text=True,
                env=env
            )
            subprocess.run(
                ["git", "config", "user.name", "Podcast Bot"],
                cwd=local_dir,
                capture_output=True,
                text=True,
                env=env
            )
            print("🔧 设置 Git 用户信息")
        except Exception as e:
            print(f"⚠️ 设置 Git 用户信息失败: {e}")

        # 添加所有更改
        subprocess.run(
            ["git", "add", "."],
            cwd=local_dir,
            capture_output=True,
            text=True,
            check=True,
            env=env
        )

        # 提交更改
        result = subprocess.run(
            ["git", "commit", "-m", commit_message],
            cwd=local_dir,
            capture_output=True,
            text=True,
            check=True,
            env=env
        )
        print(f"✅ Git commit 成功: {result.stdout}")

        # 推送到远程仓库
        result = subprocess.run(
            ["git", "push"],
            cwd=local_dir,
            capture_output=True,
            text=True,
            check=True,
            env=env
        )
        print(f"✅ Git push 成功: {result.stdout}")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Git 提交/推送失败:")
        print(f"   命令: {e.cmd}")
        print(f"   返回码: {e.returncode}")
        print(f"   标准输出: {e.stdout}")
        print(f"   错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Git 操作异常: {e}")
        return False

@app.route('/mix', methods=['POST'])
def mix_audio():
    data = request.get_json()
    # news_mp3_file = data.get('news_mp3_file')
    news_mp3_file = data["file"]
    # mp3File_name = '开源_AI_爆冷_英伟达_OCR_模型_颠覆你的代码开发_.mp3'
    NEWS_MP3_PATH = os.path.join(r'D:\n8n-docker\files', news_mp3_file)
    print(NEWS_MP3_PATH)

    if USE_ONLINE_DOWNLOAD:
        for cat, conf in SEARCH_CONFIG.items():
            trim_ms = TRIM_TARGETS.get(cat)
            fetch_and_save(cat, conf, trim_ms)

        # --- 自动查找最新intro/outro/bgm成品文件 ---
        intro_path = get_latest_mp3(os.path.join(DOWNLOAD_DIR, 'intro'))
        outro_path = get_latest_mp3(os.path.join(DOWNLOAD_DIR, 'outro'))
        bgm_path = get_latest_mp3(os.path.join(DOWNLOAD_DIR, 'bgm'))

    else:
        intro_path = get_random_mp3(os.path.join(DOWNLOAD_DIR, 'intro'))
        outro_path = get_random_mp3(os.path.join(DOWNLOAD_DIR, 'outro'))
        bgm_path = get_random_mp3(os.path.join(DOWNLOAD_DIR, 'bgm'))
        print("🎵 本地随机音乐选择：")
        print(f"  Intro: {intro_path}")
        print(f"  Outro: {outro_path}")
        print(f"  BGM:   {bgm_path}")
        # 临时裁剪intro和outro，bgm不用裁剪
        intro_path = temp_trim_audio(intro_path, 'intro', TRIM_TARGETS.get('intro'))
        outro_path = temp_trim_audio(outro_path, 'outro', TRIM_TARGETS.get('outro'))


    # bgm_path 直接用，无需裁剪

    if not (intro_path and outro_path and bgm_path and os.path.exists(NEWS_MP3_PATH)):
        print("⚠️ 自动合成失败，请检查素材文件是否齐全")
        return

    # 撞车检查
    intro_id = extract_track_id(intro_path)
    outro_id = extract_track_id(outro_path)
    bgm_id = extract_track_id(bgm_path)
    if intro_id == outro_id or intro_id == bgm_id or outro_id == bgm_id:
        print("❗⚠️ 自动检测到 intro/outro/bgm 有重复曲目（同一首歌），请更换或删除重复文件！")
        print(f"intro: {intro_path}")
        print(f"outro: {outro_path}")
        print(f"bgm:   {bgm_path}")

        # 清理临时文件
        for p in [intro_path, outro_path]:
            if is_temp_trim_file(p):
                try:
                    os.remove(p)
                    print(f"🗑️ 已清理临时文件：{p}")
                except Exception as e:
                    print(f"⚠️ 清理临时文件失败: {p} -> {e}")

        return

    out_mp3_path = auto_mix(
        news_mp3_path=NEWS_MP3_PATH,
        intro_path=intro_path,
        outro_path=outro_path,
        bgm_path=bgm_path,
        out_dir=NEWS_OUT_DIR
    )

    # 合成完后清理临时文件
    for p in [intro_path, outro_path]:
        if is_temp_trim_file(p):
            try:
                os.remove(p)
                print(f"🗑️ 已清理临时文件：{p}")
            except Exception as e:
                print(f"⚠️ 清理临时文件失败: {p} -> {e}")

    return jsonify({"status": "ok", "file": out_mp3_path})

@app.route('/upload_S3', methods=['POST'])
def upload_S3():
    # path = r"D:\n8n-docker\files"
    data = request.get_json()
    # news_mp3_file = data.get('news_mp3_file')
    print('Received data:', data)
    print('Type:', type(data))
    file_name = data["file"]
    file_path = os.path.join(r'D:\n8n-docker\files', file_name)

    # file_path = request.json.get('file_path')
    bucket = "my-n8n-audio"
    s3_key = request.json.get('s3_key', os.path.basename(file_path))

    if not os.path.isfile(file_path):
        return jsonify({"error": "File not found"}), 400
    # aws_access_key_id = '你的ACCESS_KEY',
    # aws_secret_access_key = '你的SECRET_KEY',
    # region_name = 'ap-northeast-1'

    s3 = boto3.client(
        's3'
    )

    try:
        s3.upload_file(file_path, bucket, s3_key)
        return jsonify({"status": "ok", "uploaded": s3_key})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# @app.route('/generate_bak', methods=['POST'])
# def generate():
#     data = request.json
#     prompt = data.get('prompt', 'a beautiful landscape')
#     outdir = data.get('outdir', './outputs/')
#     filename = data.get('filename', 'test.png')
#     # 调用本地 SD API
#     payload = {
#         "prompt": prompt,
#         "steps": 30,
#         "width": 1024,
#         "height": 2080,
#         "cfg_scale": 9.0,
#         "sampler_index": "Euler a"
#     }
#     try:
#         response = requests.post(SD_API_URL, json=payload)
#         response.raise_for_status()
#     except Exception as e:
#         return jsonify({"status": "fail", "msg": str(e)}), 500
#     res_json = response.json()
#     if "images" not in res_json or not res_json["images"]:
#         return jsonify({"status": "fail", "msg": "No image returned from SD"}), 500
#
#     image_base64 = res_json["images"][0]
#     # 确保输出目录存在
#     if not os.path.exists(outdir):
#         os.makedirs(outdir)
#     image_path = os.path.join(outdir, filename)
#     # 保存图片
#     with open(image_path, 'wb') as f:
#         f.write(base64.b64decode(image_base64))
#     return jsonify({"status": "ok", "file": image_path})

def start_sd_webui_in_wsl():
    launch_cmd = (
        f'source {CONDA_PATH} && '
        f'conda activate {CONDA_ENV_NAME} && '
        f'cd {SD_WEBUI_DIR} && '
        f'{PYTHON_CMD} launch.py --listen --xformers --api'
    )
    # preexec_fn=os.setsid 仅用于 Linux, Windows 下不要加
    process = subprocess.Popen(
        ['wsl', 'bash', '-c', launch_cmd],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    return process

def stop_sd_webui(process):
    # Windows 下直接 kill
    try:
        process.terminate()
        process.wait(timeout=20)
    except Exception as e:
        print(f"Stop process error: {e}")

def wait_webui_ready(timeout=180):
    url = f'http://localhost:{WEBUI_PORT}'
    for _ in range(timeout):
        try:
            r = requests.get(url, timeout=3)
            print("--------------")
            if 'Stable Diffusion' in r.text or 'Stable diffusion' in r.text:
                return True
        except Exception:
            pass
        time.sleep(1)
    return False

def generate_image(prompt, image_name):
    api_url = f'http://localhost:{WEBUI_PORT}/sdapi/v1/txt2img'
    # payload = {
    #     "prompt": prompt,
    #     "negative_prompt": "",
    #     "width": 512,
    #     "height": 512,
    #     "steps": 20,
    #     "sampler_index": "Euler a",
    # }
    # response = requests.post(api_url, json=payload, timeout=300)
    # response.raise_for_status()
    # result = response.json()
    # if "images" in result and result["images"]:
    #     import base64
    #     img_data = base64.b64decode(result["images"][0])
    #     # 确保输出目录存在
    #     os.makedirs(OUTPUT_DIR, exist_ok=True)
    #     img_path = os.path.join(OUTPUT_DIR, image_name)
    #     with open(img_path, "wb") as f:
    #         f.write(img_data)
    #     return img_path
    # return None

    payload = {
        "prompt": prompt,
        "steps": 25,
        "width": 1024,
        "height": 2080,
        "cfg_scale": 9.0,
        "sampler_index": "Euler a"
    }
    try:
        response = requests.post(api_url, json=payload, timeout=300)
        response.raise_for_status()
    except Exception as e:
        print(f"SD API request failed: {e}")
        return None
    res_json = response.json()
    if "images" not in res_json or not res_json["images"]:
        print("No image returned from SD")
        return None

    image_base64 = res_json["images"][0]
    # 确保输出目录存在
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
    image_path = os.path.join(OUTPUT_DIR, image_name)
    # 保存图片
    with open(image_path, 'wb') as f:
        f.write(base64.b64decode(image_base64))
    return image_path



# 启动 SD WebUI 进程
# 等待服务就绪
# 返回进程状态和端口信息
# 需要管理进程状态（可能需要全局变量存储进程对象）


@app.route('/start_sd_webui', methods=['POST'])
def start_sd_webui():
    """启动 Stable Diffusion WebUI"""
    global sd_webui_process, sd_webui_status

    if sd_webui_status == "running":
        return jsonify({"status": "already_running", "message": "SD WebUI 已经在运行中", "port": WEBUI_PORT})

    if sd_webui_status == "starting":
        return jsonify({"status": "starting", "message": "SD WebUI 正在启动中，请稍候", "port": WEBUI_PORT})

    try:
        sd_webui_status = "starting"
        sd_webui_process = start_sd_webui_in_wsl()

        # 等待服务就绪
        ready = wait_webui_ready(timeout=180)
        if not ready:
            stop_sd_webui(sd_webui_process)
            sd_webui_process = None
            sd_webui_status = "stopped"
            return jsonify({"status": "error", "message": "SD WebUI 启动超时"}), 500

        sd_webui_status = "running"
        return jsonify({
            "status": "success",
            "message": "SD WebUI 启动成功",
            "port": WEBUI_PORT,
            "api_url": f"http://localhost:{WEBUI_PORT}/sdapi/v1/txt2img"
        })

    except Exception as e:
        if sd_webui_process:
            stop_sd_webui(sd_webui_process)
            sd_webui_process = None
        sd_webui_status = "stopped"
        return jsonify({"status": "error", "message": f"启动失败: {str(e)}"}), 500

# 接收 prompt 和 image_name 参数
# 调用已启动的 SD WebUI API 生成图片
# 返回生成的图片路径
@app.route('/generate_image', methods=['POST'])
def generate_image_api():
    """使用已启动的 SD WebUI 生成图片"""
    global sd_webui_status

    if sd_webui_status != "running":
        return jsonify({"error": "SD WebUI 未运行，请先调用 /start_sd_webui 启动服务"}), 400

    data = request.get_json()
    prompt = data.get('prompt')
    image_name = data.get('image_name')

    if not prompt or not image_name:
        return jsonify({"error": "prompt 和 image_name 必填"}), 400

    try:
        img_path = generate_image(prompt, image_name)
        if img_path:
            return jsonify({
                "status": "success",
                "image_name": os.path.basename(img_path),
                "image_path": img_path
            })
        else:
            return jsonify({"error": "图片生成失败"}), 500
    except Exception as e:
        return jsonify({"error": f"生成图片时出错: {str(e)}"}), 500

# 停止当前运行的 SD WebUI 进程
# 清理资源
# 返回停止状态
@app.route('/stop_sd_webui', methods=['POST'])
def stop_sd_webui_api():
    """停止 Stable Diffusion WebUI"""
    global sd_webui_process, sd_webui_status

    if sd_webui_status == "stopped":
        return jsonify({"status": "already_stopped", "message": "SD WebUI 已经停止"})

    if sd_webui_status == "stopping":
        return jsonify({"status": "stopping", "message": "SD WebUI 正在停止中，请稍候"})

    try:
        sd_webui_status = "stopping"

        if sd_webui_process:
            stop_sd_webui(sd_webui_process)
            sd_webui_process = None

        sd_webui_status = "stopped"
        return jsonify({"status": "success", "message": "SD WebUI 已成功停止"})

    except Exception as e:
        sd_webui_status = "stopped"  # 即使出错也设为停止状态
        sd_webui_process = None
        return jsonify({"status": "error", "message": f"停止时出错: {str(e)}"}), 500


@app.route('/sd_webui_status', methods=['GET'])
def get_sd_webui_status():
    """获取 SD WebUI 当前状态"""
    global sd_webui_status
    return jsonify({
        "status": sd_webui_status,
        "port": WEBUI_PORT if sd_webui_status == "running" else None,
        "api_url": f"http://localhost:{WEBUI_PORT}/sdapi/v1/txt2img" if sd_webui_status == "running" else None
    })


@app.route('/reset_podcast_xml', methods=['POST'])
def reset_podcast_xml():
    """重置 podcast.xml 文件到干净状态并推送到 GitHub"""
    try:
        print("🔄 重置 podcast XML 文件...")

        # 1. 克隆或拉取仓库
        if not git_clone_or_pull(GITHUB_REPO_URL, PODCAST_REPO_DIR):
            return jsonify({"error": "无法克隆或拉取 Git 仓库"}), 500

        # 2. 创建干净的 XML 文件
        xml_file_path = os.path.join(PODCAST_REPO_DIR, PODCAST_XML_FILE)

        # 创建新的干净模板
        rss = create_podcast_xml_template()

        # 格式化并保存 XML
        rough_string = ET.tostring(rss, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")

        # 移除空行
        pretty_xml = '\n'.join([line for line in pretty_xml.split('\n') if line.strip()])

        with open(xml_file_path, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)

        print(f"✅ 成功重置 podcast XML: {xml_file_path}")

        # 3. 提交并推送更改
        commit_message = "Reset podcast.xml to clean state"
        if not git_commit_and_push(PODCAST_REPO_DIR, commit_message):
            return jsonify({"error": "Git 提交或推送失败"}), 500

        return jsonify({
            "status": "success",
            "message": "Podcast XML 文件已重置并推送成功",
            "xml_file": xml_file_path,
            "repository": GITHUB_REPO_URL
        })

    except Exception as e:
        print(f"❌ 重置 podcast XML 失败: {e}")
        return jsonify({"error": f"重置 podcast XML 失败: {str(e)}"}), 500


@app.route('/create_podcast_item', methods=['POST'])
def create_podcast_item():
    """创建新的 podcast 条目并推送到 GitHub"""
    try:
        data = request.get_json()

        # 验证必需参数
        required_fields = ['title', 'description', 'enclosure_url', 'pub_date']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"缺少必需参数: {field}"}), 400

        title = data['title']
        description = data['description']
        enclosure_url = data['enclosure_url']
        pub_date = data['pub_date']
        image_url = [None]
        # try:
        #     cover_image_url = data['pub_date']
        #     image_url[0] = cover_image_url
        # except Exception as e:
        #     print("入参没有封面图")
        if data['cover_image_url']:
            image_url[0] = data['cover_image_url']
        else:
            print("入参没有封面图=============")
        length = data.get('length', '12345678')  # 可选参数，默认值

        print(f"📻 创建 podcast 条目: {title}")

        # 1. 克隆或拉取仓库
        if not git_clone_or_pull(GITHUB_REPO_URL, PODCAST_REPO_DIR):
            return jsonify({"error": "无法克隆或拉取 Git 仓库"}), 500

        # 2. 添加新的 podcast item
        xml_file_path = os.path.join(PODCAST_REPO_DIR, PODCAST_XML_FILE)
        if image_url[0]:
            if not add_podcast_item(xml_file_path, title, description, enclosure_url, pub_date,
                                    length, image_url[0]):
                return jsonify({"error": "添加 podcast item 失败"}), 500
        else:
            if not add_podcast_item(xml_file_path, title, description, enclosure_url, pub_date, length):
                return jsonify({"error": "添加 podcast item 失败"}), 500

        # 3. 提交并推送更改
        commit_message = f"Add new podcast episode: {title}"
        if not git_commit_and_push(PODCAST_REPO_DIR, commit_message):
            return jsonify({"error": "Git 提交或推送失败"}), 500

        return jsonify({
            "status": "success",
            "message": "Podcast 条目创建并推送成功",
            "title": title,
            "xml_file": xml_file_path,
            "repository": GITHUB_REPO_URL
        })

    except Exception as e:
        print(f"❌ 创建 podcast 条目失败: {e}")
        return jsonify({"error": f"创建 podcast 条目失败: {str(e)}"}), 500


@app.route('/generate', methods=['POST'])
def generate():
    """原始的完整生成接口（保持向后兼容）"""
    # time.sleep(10)
    data = request.get_json()
    prompt = data['prompt']
    image_name = data['image_name']
    if not prompt or not image_name:
        return jsonify({"error": "prompt 和 image_name 必填"}), 400

    process = start_sd_webui_in_wsl()
    try:
        ready = wait_webui_ready(timeout=180)
        if not ready:
            stop_sd_webui(process)
            return jsonify({"error": "SD WebUI 启动超时"}), 500
        img_path = generate_image(prompt, image_name)
        stop_sd_webui(process)
        if img_path:
            return jsonify({"image_name": os.path.basename(img_path)})
        else:
            return jsonify({"error": "图片生成失败"}), 500
    except Exception as e:
        stop_sd_webui(process)
        return jsonify({"error": str(e)}), 500








if __name__ == '__main__':
    app.run(host="127.0.0.1", port=7899)
