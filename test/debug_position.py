#!/usr/bin/env python3
"""
调试封面图位置问题
"""

import requests
import json

def debug_cover_position():
    """调试封面图位置问题"""
    print("🐛 调试封面图位置问题")
    print("=" * 50)
    
    # 您提供的确切参数
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3",
        "cover_image_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   封面图: {payload['cover_image_url']}")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=60
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        print(f"📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("\n✅ 请求成功!")
                print(f"   消息: {result['message']}")
                print(f"   标题: {result['title']}")
                return True
            except json.JSONDecodeError as e:
                print(f"\n❌ JSON 解析失败: {e}")
                return False
        else:
            print("\n❌ 请求失败")
            try:
                error_info = response.json()
                print(f"   错误详情: {json.dumps(error_info, indent=2, ensure_ascii=False)}")
            except:
                print(f"   原始响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("\n❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("\n❌ 连接错误，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"\n❌ 请求异常: {e}")
        return False

def test_without_cover():
    """测试不带封面图的相同内容（对比）"""
    print("\n🔍 对比测试：不带封面图")
    print("=" * 50)
    
    payload = {
        "title": "Nvidia Test (No Cover)",
        "description": "Test without cover image",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/test_no_cover.mp3",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送请求（不带封面图）...")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=60
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 不带封面图的请求成功")
            return True
        else:
            print("❌ 不带封面图的请求也失败")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    print("🐛 封面图位置调试工具")
    print("=" * 60)
    
    # 检查服务器
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器状态异常")
            exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        exit(1)
    
    # 测试带封面图的请求
    success1 = debug_cover_position()
    
    # 测试不带封面图的请求（对比）
    success2 = test_without_cover()
    
    print("\n" + "=" * 60)
    print("📊 调试结果:")
    print(f"   带封面图: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   不带封面图: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1:
        print("\n🎉 封面图功能正常工作!")
    elif success2:
        print("\n⚠️ 基础功能正常，但封面图处理有问题")
        print("💡 可能是字符串处理逻辑的问题")
    else:
        print("\n❌ 基础功能都有问题")
        print("💡 请检查服务器日志获取详细错误信息")
