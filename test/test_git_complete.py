#!/usr/bin/env python3
import subprocess
import os
from datetime import datetime

def test_complete_git_workflow():
    """测试完整的 Git 工作流程"""
    repo_dir = r"D:\temp\podcast-feed"
    
    if not os.path.exists(repo_dir):
        print(f"目录不存在: {repo_dir}")
        return False
    
    print(f"测试完整 Git 工作流程在: {repo_dir}")
    
    try:
        # 1. Git pull
        print("1. 执行 git pull...")
        result = subprocess.run(
            ["git", "pull"],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        print(f"   git pull 返回码: {result.returncode}")
        if result.returncode != 0:
            print(f"   git pull 失败: {result.stderr}")
            return False
        
        # 2. 创建测试文件
        print("2. 创建测试文件...")
        test_file = os.path.join(repo_dir, "git_test.txt")
        with open(test_file, "w", encoding="utf-8") as f:
            f.write(f"Git 测试 - {datetime.now()}")
        print(f"   创建文件: {test_file}")
        
        # 3. Git add
        print("3. 执行 git add...")
        result = subprocess.run(
            ["git", "add", "."],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            timeout=15
        )
        print(f"   git add 返回码: {result.returncode}")
        if result.returncode != 0:
            print(f"   git add 失败: {result.stderr}")
            return False
        
        # 4. Git commit
        print("4. 执行 git commit...")
        commit_message = "Test git workflow"
        result = subprocess.run(
            ["git", "commit", "-m", commit_message],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        print(f"   git commit 返回码: {result.returncode}")
        print(f"   git commit 输出: {result.stdout}")
        if result.returncode != 0:
            print(f"   git commit 失败: {result.stderr}")
            return False
        
        # 5. Git push
        print("5. 执行 git push...")
        result = subprocess.run(
            ["git", "push"],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            timeout=60
        )
        print(f"   git push 返回码: {result.returncode}")
        print(f"   git push 输出: {result.stdout}")
        if result.returncode != 0:
            print(f"   git push 失败: {result.stderr}")
            return False
        
        print("✅ 完整 Git 工作流程测试成功!")
        return True
        
    except subprocess.TimeoutExpired as e:
        print(f"❌ Git 操作超时: {e}")
        return False
    except Exception as e:
        print(f"❌ Git 操作异常: {e}")
        return False

if __name__ == "__main__":
    success = test_complete_git_workflow()
    if success:
        print("\n🎉 Git 配置完全正常!")
        print("✅ 可以正常进行 pull、add、commit、push 操作")
    else:
        print("\n❌ Git 操作有问题")
        print("💡 请检查具体的错误信息")
