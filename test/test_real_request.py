#!/usr/bin/env python3
"""
使用真实请求参数测试封面图功能
"""

import requests
import json

def test_real_request():
    """使用您提供的真实参数测试"""
    print("🎙️ 使用真实参数测试 Podcast API")
    print("=" * 60)
    
    # 您提供的真实参数
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3",
        "cover_image_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   音频URL: {payload['enclosure_url']}")
    print(f"   封面图URL: {payload['cover_image_url']}")
    print(f"   发布时间: {payload['pub_date']}")
    
    try:
        print("\n⏳ 正在处理请求...")
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=120
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        print(f"📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("\n🎉 成功创建 Podcast 条目！")
                print(f"   消息: {result['message']}")
                print(f"   标题: {result['title']}")
                print(f"   XML文件: {result['xml_file']}")
                print(f"   仓库: {result['repository']}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                return False
        else:
            print("❌ 创建失败")
            try:
                error_info = response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   原始响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_without_cover():
    """测试不带封面图的相同内容（对比测试）"""
    print("\n🎙️ 对比测试：不带封面图的相同内容")
    print("=" * 60)
    
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI! (No Cover)",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix_no_cover.mp3",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
        # 注意：没有 cover_image_url
    }
    
    print("📤 发送请求（不带封面图）...")
    print(f"   标题: {payload['title']}")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=120
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 不带封面图的请求成功")
            return True
        else:
            print("❌ 不带封面图的请求也失败")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

if __name__ == "__main__":
    print("🧪 真实参数测试工具")
    print("=" * 70)
    
    # 检查服务器状态
    if not check_server_status():
        print("请确保服务器运行在 http://127.0.0.1:7899")
        exit(1)
    
    print("\n🎯 使用您提供的真实参数进行测试...")
    
    # 测试1：带封面图的真实请求
    success1 = test_real_request()
    
    # 测试2：不带封面图的对比测试
    success2 = test_without_cover()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    print(f"   带封面图的真实请求: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   不带封面图的对比测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1:
        print("\n🎉 真实参数测试成功！封面图功能正常")
        print("✅ 您的 Podcast 条目已创建并推送到 GitHub")
    elif success2:
        print("\n⚠️ 基础功能正常，但封面图功能有问题")
        print("💡 建议检查封面图 URL 或 XML 处理逻辑")
    else:
        print("\n❌ 所有测试都失败，可能是服务器配置问题")
        print("💡 建议检查服务器日志获取详细错误信息")
