# Podcast API 快速设置指南

本指南帮助您快速设置和使用 Podcast RSS Feed API。

## 前置要求

### 1. 系统要求
- Python 3.7+
- Git 已安装并配置
- 网络连接

### 2. Python 依赖
确保已安装以下 Python 包：
```bash
pip install flask requests xml
```

### 3. Git 配置

#### 配置 SSH 密钥（推荐）
```bash
# 生成 SSH 密钥（如果还没有）
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 添加到 SSH agent
ssh-add ~/.ssh/id_rsa

# 复制公钥到 GitHub
cat ~/.ssh/id_rsa.pub
```

然后将公钥添加到 GitHub 账户的 SSH 密钥中。

#### 验证 SSH 连接
```bash
ssh -T **************
```

## 快速开始

### 1. 启动服务器

```bash
cd test
python cmd_server.py
```

服务器将在 `http://127.0.0.1:7899` 启动。

### 2. 创建第一个 Podcast 条目

#### 使用 cURL
```bash
curl -X POST http://127.0.0.1:7899/create_podcast_item \
  -H "Content-Type: application/json" \
  -d '{
    "title": "第1期：AI 让你不焦虑",
    "description": "本期内容讲述AI如何帮助内容创作者减压",
    "enclosure_url": "https://yourdomain.com/audio/ep1.mp3",
    "pub_date": "Mon, 01 Jul 2025 12:00:00 +0800",
    "length": "12345678"
  }'
```

#### 使用 Python 脚本
```bash
python test_podcast_api.py
```

#### 使用示例脚本
```bash
python podcast_example.py
```

### 3. 验证结果

1. **检查 GitHub 仓库**: 访问 `https://github.com/fjxc2021/podcast-feed`
2. **查看 podcast.xml**: 确认新条目已添加
3. **检查 Git 提交**: 查看提交历史

## 配置自定义

### 1. 修改 GitHub 仓库

在 `cmd_server.py` 中修改：
```python
GITHUB_REPO_URL = "**************:your-username/your-podcast-repo.git"
```

### 2. 修改本地目录

```python
PODCAST_REPO_DIR = r'D:\your\custom\path\podcast-feed'
```

### 3. 自定义 RSS Feed 信息

修改 `create_podcast_xml_template()` 函数：
```python
def create_podcast_xml_template():
    # ... 现有代码 ...
    
    # 修改这些值
    ET.SubElement(channel, "title").text = "您的播客名称"
    ET.SubElement(channel, "description").text = "您的播客描述"
    ET.SubElement(channel, "link").text = "https://your-website.com"
    ET.SubElement(channel, "itunes:author").text = "您的名字"
    # ...
```

## 测试和验证

### 1. 运行完整测试
```bash
python test_podcast_api.py
```

选择选项 4 运行所有测试。

### 2. 运行示例
```bash
python podcast_example.py
```

### 3. 手动验证

#### 检查 XML 格式
```bash
# 在仓库目录中
xmllint --format podcast.xml
```

#### 验证 RSS Feed
使用在线 RSS 验证器：
- https://validator.w3.org/feed/
- https://podba.se/validate/

## 常见问题解决

### 1. Git 权限问题

**错误**: `Permission denied (publickey)`

**解决**:
```bash
# 检查 SSH 密钥
ssh-add -l

# 重新添加密钥
ssh-add ~/.ssh/id_rsa

# 测试连接
ssh -T **************
```

### 2. 仓库不存在

**错误**: `Repository not found`

**解决**:
1. 确认仓库 URL 正确
2. 检查仓库是否存在
3. 验证访问权限

### 3. XML 格式错误

**错误**: XML 解析失败

**解决**:
1. 删除现有的 `podcast.xml` 文件
2. 重新运行 API 创建新文件
3. 检查特殊字符是否正确转义

### 4. 网络连接问题

**错误**: 连接超时

**解决**:
1. 检查网络连接
2. 验证防火墙设置
3. 尝试使用 HTTPS 克隆

## 高级用法

### 1. 批量导入

创建批量导入脚本：
```python
import requests
import json

episodes = [
    {
        "title": "第1期：标题",
        "description": "描述",
        "enclosure_url": "https://example.com/ep1.mp3",
        "pub_date": "Mon, 01 Jul 2025 12:00:00 +0800"
    },
    # 更多条目...
]

for episode in episodes:
    response = requests.post(
        "http://127.0.0.1:7899/create_podcast_item",
        json=episode
    )
    print(f"创建 {episode['title']}: {response.status_code}")
```

### 2. 自动化工作流

使用 cron 或任务调度器定期创建条目：
```bash
# 每天上午 9 点运行
0 9 * * * /usr/bin/python3 /path/to/your/podcast_script.py
```

### 3. 集成到其他系统

将 API 集成到内容管理系统或自动化工具中：
- n8n 工作流
- Zapier 自动化
- GitHub Actions
- 自定义脚本

## 监控和维护

### 1. 日志监控

服务器会输出详细日志，建议：
- 定期检查日志文件
- 设置错误告警
- 监控磁盘空间

### 2. 备份策略

- 定期备份 Git 仓库
- 保存重要的 podcast.xml 版本
- 备份服务器配置

### 3. 性能优化

- 定期清理临时文件
- 监控内存使用
- 优化 Git 操作频率

## 支持和帮助

### 文档资源
- `PODCAST_API_README.md` - 详细 API 文档
- `test_podcast_api.py` - 测试脚本
- `podcast_example.py` - 使用示例

### 故障排除
1. 检查服务器日志
2. 验证配置参数
3. 测试网络连接
4. 确认 Git 权限

### 获取帮助
如果遇到问题：
1. 查看错误日志
2. 检查配置文件
3. 运行测试脚本
4. 参考文档和示例
