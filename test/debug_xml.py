#!/usr/bin/env python3
"""
调试 XML 处理问题
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom
from datetime import datetime
import os

def test_xml_creation():
    """测试 XML 创建"""
    print("🧪 测试 XML 创建...")
    
    try:
        # 创建基本的 RSS 结构
        rss = ET.Element("rss", version="2.0")
        rss.set("xmlns:itunes", "http://www.itunes.com/dtds/podcast-1.0.dtd")
        
        channel = ET.SubElement(rss, "channel")
        
        # 基本信息
        ET.SubElement(channel, "title").text = "Test Podcast"
        ET.SubElement(channel, "description").text = "Test Description"
        ET.SubElement(channel, "link").text = "https://example.com"
        ET.SubElement(channel, "language").text = "en-US"
        ET.SubElement(channel, "copyright").text = f"© {datetime.now().year} Test"
        ET.SubElement(channel, "lastBuildDate").text = datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")
        
        # iTunes 特定标签
        ET.SubElement(channel, "itunes:author").text = "Test Author"
        ET.SubElement(channel, "itunes:summary").text = "Test Summary"
        ET.SubElement(channel, "itunes:category", text="Technology")
        
        print("✅ 基本 RSS 结构创建成功")
        
        # 添加一个 item
        item = ET.Element("item")
        ET.SubElement(item, "title").text = "Test Episode"
        ET.SubElement(item, "description").text = "Test Episode Description"
        
        # 创建 enclosure 元素
        enclosure = ET.SubElement(item, "enclosure")
        enclosure.set("url", "https://example.com/test.mp3")
        enclosure.set("length", "12345678")
        enclosure.set("type", "audio/mpeg")
        
        # 添加封面图占位符
        cover_placeholder = ET.SubElement(item, "COVER_IMAGE_PLACEHOLDER")
        cover_placeholder.text = "https://example.com/cover.jpg"
        
        ET.SubElement(item, "pubDate").text = "Fri, 09 May 2025 07:34:01 GMT"
        
        # 添加 GUID
        guid = ET.SubElement(item, "guid")
        guid.text = "https://example.com/test.mp3"
        guid.set("isPermaLink", "true")
        
        # 将 item 添加到 channel
        channel.insert(0, item)
        
        print("✅ Item 添加成功")
        
        # 转换为字符串
        rough_string = ET.tostring(rss, encoding='unicode')
        print("✅ XML 转换为字符串成功")
        
        # 处理封面图占位符
        cover_url = "https://example.com/cover.jpg"
        placeholder = f"<COVER_IMAGE_PLACEHOLDER>{cover_url}</COVER_IMAGE_PLACEHOLDER>"
        itunes_tag = f'<itunes:image href="{cover_url}"/>'
        rough_string = rough_string.replace(placeholder, itunes_tag)
        print("✅ 封面图占位符替换成功")
        
        # 格式化 XML
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")
        print("✅ XML 格式化成功")
        
        # 移除空行
        pretty_xml = '\n'.join([line for line in pretty_xml.split('\n') if line.strip()])
        print("✅ 空行移除成功")
        
        # 保存到文件
        test_file = "test_podcast.xml"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)
        print(f"✅ XML 文件保存成功: {test_file}")
        
        # 显示文件内容
        print("\n📄 生成的 XML 内容:")
        print(pretty_xml[:500] + "..." if len(pretty_xml) > 500 else pretty_xml)
        
        return True
        
    except Exception as e:
        print(f"❌ XML 测试失败: {e}")
        import traceback
        print(f"❌ 详细错误: {traceback.format_exc()}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n🗂️ 测试文件操作...")
    
    try:
        # 测试目录创建
        test_dir = r"D:\temp\podcast-feed"
        if not os.path.exists(test_dir):
            os.makedirs(test_dir, exist_ok=True)
            print(f"✅ 目录创建成功: {test_dir}")
        else:
            print(f"✅ 目录已存在: {test_dir}")
        
        # 测试文件写入
        test_file = os.path.join(test_dir, "test.xml")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("<?xml version='1.0' encoding='UTF-8'?>\n<test>Hello World</test>")
        print(f"✅ 文件写入成功: {test_file}")
        
        # 测试文件读取
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ 文件读取成功: {len(content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        import traceback
        print(f"❌ 详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🔧 XML 处理调试工具")
    print("=" * 50)
    
    # 测试 XML 创建
    xml_success = test_xml_creation()
    
    # 测试文件操作
    file_success = test_file_operations()
    
    print("\n" + "=" * 50)
    print("📊 调试结果:")
    print(f"   XML 处理: {'✅ 成功' if xml_success else '❌ 失败'}")
    print(f"   文件操作: {'✅ 成功' if file_success else '❌ 失败'}")
    
    if xml_success and file_success:
        print("\n🎉 所有测试通过！XML 处理功能正常")
        print("💡 问题可能出在其他地方，如 Git 操作或参数处理")
    else:
        print("\n❌ 发现问题，需要修复基础功能")
