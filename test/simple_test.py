#!/usr/bin/env python3
"""
简单的 Podcast API 测试
"""

import requests
import json
from datetime import datetime

def test_podcast_api():
    """测试 podcast API"""
    print("🎙️ 测试 Podcast API")
    print("=" * 40)
    
    # 生成当前时间的 RFC 2822 格式
    pub_date = datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")
    
    payload = {
        "title": "第1期：AI 让你不焦虑",
        "description": "本期内容讲述AI如何帮助内容创作者减压",
        "enclosure_url": "https://yourdomain.com/audio/ep1.mp3",
        "pub_date": pub_date,
        "length": "12345678"
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   描述: {payload['description']}")
    print(f"   音频URL: {payload['enclosure_url']}")
    print(f"   发布时间: {payload['pub_date']}")
    
    try:
        print("\n⏳ 正在处理请求（可能需要一些时间）...")
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=120
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("🎉 成功创建 Podcast 条目！")
            print(f"   消息: {result['message']}")
            print(f"   标题: {result['title']}")
            print(f"   XML文件: {result['xml_file']}")
            print(f"   仓库: {result['repository']}")
            print("\n✅ 请检查 GitHub 仓库确认更新:")
            print("   https://github.com/fjxc2021/podcast-feed")
            return True
        else:
            print("❌ 创建失败")
            try:
                error_info = response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    # 先检查服务器状态
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器状态异常")
            exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        exit(1)
    
    # 运行测试
    success = test_podcast_api()
    
    if success:
        print("\n🎉 测试完成！Podcast API 工作正常")
    else:
        print("\n❌ 测试失败，请检查服务器日志")
