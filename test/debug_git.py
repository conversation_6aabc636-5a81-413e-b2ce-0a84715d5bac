#!/usr/bin/env python3
"""
调试 Git 操作
"""

import os
import subprocess
import tempfile
import shutil

def get_git_env():
    """获取 Git 操作所需的环境变量"""
    env = os.environ.copy()
    
    # 确保 SSH 相关环境变量
    user_home = os.path.expanduser("~")
    ssh_dir = os.path.join(user_home, ".ssh")
    
    # 设置 SSH 相关环境变量
    env["HOME"] = user_home
    env["SSH_AUTH_SOCK"] = env.get("SSH_AUTH_SOCK", "")
    
    # 如果是 Windows，可能需要设置 GIT_SSH_COMMAND
    if os.name == 'nt':  # Windows
        # 尝试多种密钥类型
        key_types = ["id_ed25519", "id_rsa", "id_ecdsa"]
        ssh_key_path = None
        
        for key_type in key_types:
            potential_key = os.path.join(ssh_dir, key_type)
            if os.path.exists(potential_key):
                ssh_key_path = potential_key
                break
        
        if ssh_key_path:
            env["GIT_SSH_COMMAND"] = f'ssh -i "{ssh_key_path}" -o StrictHostKeyChecking=no'
            print(f"🔑 使用 SSH 密钥: {ssh_key_path}")
        else:
            print(f"⚠️ 未找到 SSH 密钥在: {ssh_dir}")
    
    return env

def test_ssh_github():
    """测试 SSH 连接到 GitHub"""
    print("🔍 测试 SSH 连接到 GitHub...")
    
    try:
        env = get_git_env()
        result = subprocess.run(
            ["ssh", "-T", "**************"],
            capture_output=True,
            text=True,
            timeout=10,
            env=env
        )
        
        print(f"返回码: {result.returncode}")
        print(f"标准输出: {result.stdout}")
        print(f"错误输出: {result.stderr}")
        
        if "successfully authenticated" in result.stderr:
            print("✅ SSH 连接成功")
            return True
        else:
            print("❌ SSH 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ SSH 测试异常: {e}")
        return False

def test_git_clone():
    """测试 Git 克隆"""
    print("\n📥 测试 Git 克隆...")
    
    repo_url = "**************:fjxc2021/podcast-feed.git"
    test_dir = r"D:\temp\debug-git-test"
    
    # 清理现有目录
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
        print(f"🗑️ 清理现有目录: {test_dir}")
    
    try:
        env = get_git_env()
        
        # 确保父目录存在
        os.makedirs(os.path.dirname(test_dir), exist_ok=True)
        
        print(f"克隆仓库: {repo_url}")
        print(f"目标目录: {test_dir}")
        
        result = subprocess.run(
            ["git", "clone", repo_url, test_dir],
            capture_output=True,
            text=True,
            timeout=30,
            env=env
        )
        
        print(f"返回码: {result.returncode}")
        print(f"标准输出: {result.stdout}")
        print(f"错误输出: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ Git 克隆成功")
            
            # 列出克隆的文件
            if os.path.exists(test_dir):
                files = os.listdir(test_dir)
                print(f"克隆的文件: {files}")
            
            return test_dir
        else:
            print("❌ Git 克隆失败")
            return None
            
    except Exception as e:
        print(f"❌ Git 克隆异常: {e}")
        return None

def test_git_operations(repo_dir):
    """测试 Git 提交和推送操作"""
    print(f"\n📝 测试 Git 操作在目录: {repo_dir}")
    
    if not repo_dir or not os.path.exists(repo_dir):
        print("❌ 仓库目录不存在")
        return False
    
    try:
        env = get_git_env()
        
        # 创建一个测试文件
        test_file = os.path.join(repo_dir, "debug_test.txt")
        with open(test_file, "w", encoding="utf-8") as f:
            f.write(f"调试测试文件 - {os.getpid()}\n")
        print(f"📄 创建测试文件: {test_file}")
        
        # Git add
        print("📤 执行 git add...")
        result = subprocess.run(
            ["git", "add", "."],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            env=env
        )
        print(f"git add 返回码: {result.returncode}")
        if result.stderr:
            print(f"git add 错误: {result.stderr}")
        
        # Git commit
        print("📤 执行 git commit...")
        commit_message = "Debug test commit"
        result = subprocess.run(
            ["git", "commit", "-m", commit_message],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            env=env
        )
        print(f"git commit 返回码: {result.returncode}")
        print(f"git commit 输出: {result.stdout}")
        if result.stderr:
            print(f"git commit 错误: {result.stderr}")
        
        if result.returncode != 0:
            print("❌ Git commit 失败")
            return False
        
        # Git push
        print("📤 执行 git push...")
        result = subprocess.run(
            ["git", "push"],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            env=env
        )
        print(f"git push 返回码: {result.returncode}")
        print(f"git push 输出: {result.stdout}")
        if result.stderr:
            print(f"git push 错误: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ Git push 成功")
            return True
        else:
            print("❌ Git push 失败")
            return False
            
    except Exception as e:
        print(f"❌ Git 操作异常: {e}")
        return False

def main():
    print("🔧 Git 操作调试工具")
    print("=" * 50)
    
    # 1. 测试 SSH 连接
    if not test_ssh_github():
        print("\n❌ SSH 连接失败，无法继续")
        return
    
    # 2. 测试 Git 克隆
    repo_dir = test_git_clone()
    if not repo_dir:
        print("\n❌ Git 克隆失败，无法继续")
        return
    
    # 3. 测试 Git 操作
    if test_git_operations(repo_dir):
        print("\n🎉 所有 Git 操作测试成功！")
        print("✅ Podcast API 应该可以正常工作了")
    else:
        print("\n❌ Git 操作测试失败")
        print("💡 请检查 GitHub 仓库权限和网络连接")

if __name__ == "__main__":
    main()
