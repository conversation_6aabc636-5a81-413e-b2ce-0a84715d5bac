#!/usr/bin/env python3
import requests
import json

def test_your_params():
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3",
        "cover_image_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("Testing your parameters...")
    print(f"Title: {payload['title']}")
    print(f"Cover: {payload['cover_image_url']}")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=90
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS!")
            print(f"Message: {result.get('message')}")
            return True
        else:
            print("FAILED!")
            return False
            
    except Exception as e:
        print(f"Exception: {e}")
        return False

if __name__ == "__main__":
    test_your_params()
