#!/usr/bin/env python3
"""
简单快速测试
"""

import requests
import json

def quick_test():
    """快速测试"""
    print("⚡ 快速测试")
    
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/test.mp3",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("发送请求...")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text[:500]}...")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"错误: {e}")
        return False

if __name__ == "__main__":
    quick_test()
