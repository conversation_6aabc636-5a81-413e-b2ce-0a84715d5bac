#!/usr/bin/env python3
"""
测试修复后的封面图功能
"""

import requests
import json
from datetime import datetime

def test_cover_image_fix():
    """测试修复后的封面图功能"""
    print("🔧 测试修复后的封面图功能")
    print("=" * 50)
    
    payload = {
        "title": "测试封面图修复版",
        "description": "测试修复后的封面图功能是否正常工作",
        "enclosure_url": "https://example.com/test-fix.mp3",
        "pub_date": datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800"),
        "length": "5000000",
        "cover_image_url": "https://s3-ap-southeast-1.amazonaws.com/test-cover.png"
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   封面图: {payload['cover_image_url']}")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=90
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("🎉 封面图功能修复成功！")
            print(f"   消息: {result['message']}")
            print(f"   标题: {result['title']}")
            print(f"   XML文件: {result['xml_file']}")
            return True
        else:
            print("❌ 仍然失败")
            try:
                error_info = response.json()
                print(f"   错误: {error_info}")
            except:
                print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    print("🛠️ 封面图功能修复测试")
    print("=" * 60)
    
    # 检查服务器
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器状态异常")
            exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        exit(1)
    
    # 测试修复
    success = test_cover_image_fix()
    
    if success:
        print("\n🎉 修复成功！封面图功能正常工作")
        print("✅ 现在可以正常使用 cover_image_url 参数了")
    else:
        print("\n❌ 修复失败，需要进一步调试")
