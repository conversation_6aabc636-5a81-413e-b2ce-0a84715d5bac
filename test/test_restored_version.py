#!/usr/bin/env python3
"""
测试还原版本
"""

import requests
import json

def test_restored_version():
    """测试还原版本的功能"""
    print("🔄 测试还原版本")
    print("=" * 50)
    
    # 您提供的确切参数
    payload = {
        "title": "Nvidia Drops a Code AI Bomb: Open Source Model Crushes OpenAI!",
        "description": "NVIDIA just open-sourced OCR (Open Code Reasoning) – a suite of powerful AI models designed for code generation and reasoning! Built on the Nemotron architecture, these models (available in 32B, 14B, and 7B sizes) outperform OpenAI's o3-Mini and o1 on the LiveCodeBench benchmark. This release, powered by a high-quality code training dataset, promises to be a game-changer for developers using popular frameworks.",
        "enclosure_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!_final_mix.mp3",
        "cover_image_url": "https://my-n8n-audio.s3.ap-southeast-1.amazonaws.com/Nvidia_Drops_a_Code_AI_Bomb__Open_Source_Model_Crushes_OpenAI!.png",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送请求...")
    print(f"   标题: {payload['title']}")
    print(f"   描述长度: {len(payload['description'])} 字符")
    print(f"   音频URL: {payload['enclosure_url']}")
    print(f"   封面图URL: {payload['cover_image_url']}")
    print(f"   发布时间: {payload['pub_date']}")
    
    print(f"\n📋 完整请求体:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    
    try:
        print("\n⏳ 发送请求到服务器...")
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=120
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("\n🎉 成功!")
                print(f"   状态: {result.get('status', 'N/A')}")
                print(f"   消息: {result.get('message', 'N/A')}")
                print(f"   标题: {result.get('title', 'N/A')}")
                print(f"   XML文件: {result.get('xml_file', 'N/A')}")
                print(f"   仓库: {result.get('repository', 'N/A')}")
                return True
            except json.JSONDecodeError as e:
                print(f"\n❌ JSON 解析失败: {e}")
                print(f"📥 原始响应内容: {response.text}")
                return False
        else:
            print(f"\n❌ 请求失败")
            print(f"📥 响应内容: {response.text}")
            
            try:
                error_info = response.json()
                print(f"📥 错误详情: {json.dumps(error_info, indent=2, ensure_ascii=False)}")
            except:
                print("📥 无法解析错误响应为 JSON")
            
            return False
            
    except requests.exceptions.Timeout:
        print("\n❌ 请求超时 (120秒)")
        print("💡 可能的原因:")
        print("   - 服务器处理时间过长")
        print("   - Git 操作卡住")
        print("   - XML 处理问题")
        return False
    except requests.exceptions.ConnectionError:
        print("\n❌ 连接错误")
        print("💡 请确保:")
        print("   - 服务器正在运行")
        print("   - 端口 7899 可访问")
        return False
    except Exception as e:
        print(f"\n❌ 请求异常: {e}")
        return False

def test_simple_case():
    """测试简单情况作为对比"""
    print("\n🧪 对比测试：简单参数")
    print("=" * 50)
    
    simple_payload = {
        "title": "Simple Test",
        "description": "Simple description for testing",
        "enclosure_url": "https://example.com/simple.mp3",
        "pub_date": "Fri, 09 May 2025 07:34:01 GMT"
    }
    
    print("📤 发送简单请求...")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=simple_payload, 
            timeout=60
        )
        
        print(f"📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 简单参数测试成功")
            return True
        else:
            print("❌ 简单参数测试也失败")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 简单测试异常: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请确保服务器运行在 http://127.0.0.1:7899")
        return False
    except Exception as e:
        print(f"❌ 服务器检查异常: {e}")
        return False

if __name__ == "__main__":
    print("🔄 还原版本测试工具")
    print("=" * 70)
    
    # 检查服务器状态
    if not check_server_status():
        exit(1)
    
    # 测试您的完整参数
    success1 = test_restored_version()
    
    # 测试简单参数作为对比
    success2 = test_simple_case()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    print(f"   完整参数测试: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   简单参数测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1:
        print("\n🎉 您的参数测试成功!")
        print("✅ Podcast 条目已创建")
        print("✅ 封面图参数已接收")
    elif success2:
        print("\n⚠️ 简单参数成功，但完整参数失败")
        print("💡 可能的问题:")
        print("   - 长描述文本处理")
        print("   - 特殊字符或URL格式")
        print("   - 封面图参数处理")
    else:
        print("\n❌ 所有测试都失败")
        print("💡 建议:")
        print("   - 检查服务器控制台日志")
        print("   - 确认服务器配置")
        print("   - 验证 Git 和网络连接")
