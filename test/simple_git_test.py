#!/usr/bin/env python3
import subprocess
import os

def test_git_pull():
    """测试简单的 git pull"""
    repo_dir = r"D:\temp\podcast-feed"
    
    if not os.path.exists(repo_dir):
        print(f"目录不存在: {repo_dir}")
        return False
    
    print(f"测试 git pull 在: {repo_dir}")
    
    try:
        result = subprocess.run(
            ["git", "pull"],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        print(f"错误: {result.stderr}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"异常: {e}")
        return False

if __name__ == "__main__":
    test_git_pull()
