#!/usr/bin/env python3
"""
调试封面图功能
"""

import requests
import json
from datetime import datetime

def test_simple_request():
    """测试简单的请求"""
    print("🔍 测试简单的 Podcast 请求（不带封面图）")
    print("=" * 50)
    
    payload = {
        "title": "测试条目",
        "description": "测试描述",
        "enclosure_url": "https://example.com/test.mp3",
        "pub_date": datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")
    }
    
    print("📤 发送请求（不带封面图）...")
    print(f"   Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=60
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        print(f"📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 不带封面图的请求成功")
            return True
        else:
            print("❌ 不带封面图的请求失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_cover_image_request():
    """测试带封面图的请求"""
    print("\n🔍 测试带封面图的 Podcast 请求")
    print("=" * 50)
    
    payload = {
        "title": "测试条目（带封面图）",
        "description": "测试描述（带封面图）",
        "enclosure_url": "https://example.com/test-cover.mp3",
        "pub_date": datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800"),
        "cover_image_url": "https://example.com/cover.jpg"
    }
    
    print("📤 发送请求（带封面图）...")
    print(f"   Payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            "http://127.0.0.1:7899/create_podcast_item", 
            json=payload, 
            timeout=60
        )
        
        print(f"\n📥 响应状态码: {response.status_code}")
        print(f"📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 带封面图的请求成功")
            return True
        else:
            print("❌ 带封面图的请求失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        response = requests.get("http://127.0.0.1:7899/sd_webui_status", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

if __name__ == "__main__":
    print("🐛 封面图功能调试工具")
    print("=" * 60)
    
    # 检查服务器状态
    if not check_server_status():
        print("请确保服务器运行在 http://127.0.0.1:7899")
        exit(1)
    
    # 测试1：不带封面图（基础功能）
    success1 = test_simple_request()
    
    # 测试2：带封面图（新功能）
    success2 = test_cover_image_request()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 调试结果:")
    print(f"   不带封面图: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   带封面图: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if not success1:
        print("\n❌ 基础功能失败，可能是服务器配置问题")
    elif not success2:
        print("\n❌ 封面图功能失败，可能是新代码问题")
    else:
        print("\n🎉 所有功能正常！")
