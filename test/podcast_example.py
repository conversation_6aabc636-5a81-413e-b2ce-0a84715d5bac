#!/usr/bin/env python3
"""
Podcast API 使用示例
演示如何使用 podcast 接口创建和管理 podcast feed
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://127.0.0.1:7899"

def create_single_episode():
    """示例：创建单个 podcast 条目"""
    print("🎙️ 示例：创建单个 Podcast 条目")
    print("-" * 40)
    
    # 生成当前时间的 RFC 2822 格式
    pub_date = datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")
    
    payload = {
        "title": "第1期：AI 让你不焦虑",
        "description": "本期内容讲述AI如何帮助内容创作者减压，分享实用的AI工具和技巧",
        "enclosure_url": "https://yourdomain.com/audio/ep1.mp3",
        "pub_date": pub_date,
        "length": "15678900"  # 音频文件大小（字节）
    }
    
    print(f"📝 创建条目信息:")
    print(f"   标题: {payload['title']}")
    print(f"   描述: {payload['description']}")
    print(f"   音频URL: {payload['enclosure_url']}")
    print(f"   发布时间: {payload['pub_date']}")
    print(f"   文件大小: {payload['length']} 字节")
    
    try:
        response = requests.post(f"{BASE_URL}/create_podcast_item", json=payload)
        if response.status_code == 200:
            result = response.json()
            print(f"\n✅ 创建成功!")
            print(f"   消息: {result['message']}")
            print(f"   XML文件: {result['xml_file']}")
            print(f"   仓库: {result['repository']}")
        else:
            print(f"\n❌ 创建失败: {response.json()}")
    except Exception as e:
        print(f"\n❌ 请求失败: {e}")

def create_weekly_series():
    """示例：创建一周的播客系列"""
    print("\n🗓️ 示例：创建一周的播客系列")
    print("-" * 40)
    
    # 定义一周的播客内容
    weekly_episodes = [
        {
            "title": "周一：技术新闻回顾",
            "description": "回顾本周最重要的技术新闻和趋势分析",
            "url": "https://yourdomain.com/audio/monday-tech-news.mp3"
        },
        {
            "title": "周二：开发者工具推荐",
            "description": "分享提高开发效率的工具和技巧",
            "url": "https://yourdomain.com/audio/tuesday-dev-tools.mp3"
        },
        {
            "title": "周三：代码质量专题",
            "description": "探讨如何编写更好的代码和最佳实践",
            "url": "https://yourdomain.com/audio/wednesday-code-quality.mp3"
        },
        {
            "title": "周四：职业发展指南",
            "description": "程序员职业发展路径和技能提升建议",
            "url": "https://yourdomain.com/audio/thursday-career.mp3"
        },
        {
            "title": "周五：技术趋势展望",
            "description": "分析未来技术趋势和行业发展方向",
            "url": "https://yourdomain.com/audio/friday-trends.mp3"
        }
    ]
    
    base_date = datetime.now()
    successful_count = 0
    
    for i, episode in enumerate(weekly_episodes):
        # 计算发布日期（从今天开始，每天一期）
        pub_date = (base_date + timedelta(days=i)).strftime("%a, %d %b %Y 09:00:00 +0800")
        
        payload = {
            "title": episode["title"],
            "description": episode["description"],
            "enclosure_url": episode["url"],
            "pub_date": pub_date,
            "length": str(18000000 + i * 2000000)  # 递增的文件大小
        }
        
        print(f"\n📻 创建: {episode['title']}")
        print(f"   发布时间: {pub_date}")
        
        try:
            response = requests.post(f"{BASE_URL}/create_podcast_item", json=payload)
            if response.status_code == 200:
                print(f"   ✅ 成功创建")
                successful_count += 1
            else:
                print(f"   ❌ 创建失败: {response.json()}")
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
    
    print(f"\n📊 系列创建完成: {successful_count}/{len(weekly_episodes)} 成功")

def create_themed_episodes():
    """示例：创建主题系列播客"""
    print("\n🎯 示例：创建主题系列播客")
    print("-" * 40)
    
    # AI 主题系列
    ai_series = [
        {
            "title": "AI系列第1期：机器学习基础",
            "description": "深入浅出地介绍机器学习的基本概念和应用场景",
            "url": "https://yourdomain.com/audio/ai-series-01-ml-basics.mp3"
        },
        {
            "title": "AI系列第2期：深度学习入门",
            "description": "从神经网络到深度学习的完整学习路径",
            "url": "https://yourdomain.com/audio/ai-series-02-deep-learning.mp3"
        },
        {
            "title": "AI系列第3期：自然语言处理",
            "description": "探索NLP技术在实际项目中的应用",
            "url": "https://yourdomain.com/audio/ai-series-03-nlp.mp3"
        },
        {
            "title": "AI系列第4期：计算机视觉",
            "description": "图像识别和计算机视觉技术详解",
            "url": "https://yourdomain.com/audio/ai-series-04-computer-vision.mp3"
        }
    ]
    
    print(f"🤖 创建 AI 主题系列 ({len(ai_series)} 期)")
    
    base_date = datetime.now()
    
    for i, episode in enumerate(ai_series):
        # 每周发布一期
        pub_date = (base_date + timedelta(weeks=i)).strftime("%a, %d %b %Y 10:00:00 +0800")
        
        payload = {
            "title": episode["title"],
            "description": episode["description"],
            "enclosure_url": episode["url"],
            "pub_date": pub_date,
            "length": str(25000000 + i * 3000000)  # 较大的文件
        }
        
        print(f"\n🎙️ {episode['title']}")
        print(f"   📅 {pub_date}")
        
        try:
            response = requests.post(f"{BASE_URL}/create_podcast_item", json=payload)
            if response.status_code == 200:
                print(f"   ✅ 创建成功")
            else:
                print(f"   ❌ 失败: {response.json()}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def create_interview_series():
    """示例：创建访谈系列"""
    print("\n🎤 示例：创建访谈系列")
    print("-" * 40)
    
    interviews = [
        {
            "guest": "张三",
            "role": "资深前端工程师",
            "topic": "React 生态系统的演进",
            "url": "https://yourdomain.com/audio/interview-zhangsan-react.mp3"
        },
        {
            "guest": "李四",
            "role": "AI 研究员",
            "topic": "大语言模型的未来发展",
            "url": "https://yourdomain.com/audio/interview-lisi-llm.mp3"
        },
        {
            "guest": "王五",
            "role": "创业者",
            "topic": "技术创业的挑战与机遇",
            "url": "https://yourdomain.com/audio/interview-wangwu-startup.mp3"
        }
    ]
    
    base_date = datetime.now()
    
    for i, interview in enumerate(interviews):
        title = f"专访{interview['guest']}：{interview['topic']}"
        description = f"本期我们邀请到了{interview['role']}{interview['guest']}，深入探讨{interview['topic']}。"
        
        # 每两周一期访谈
        pub_date = (base_date + timedelta(weeks=i*2)).strftime("%a, %d %b %Y 14:00:00 +0800")
        
        payload = {
            "title": title,
            "description": description,
            "enclosure_url": interview["url"],
            "pub_date": pub_date,
            "length": str(35000000 + i * 5000000)  # 访谈通常较长
        }
        
        print(f"\n🎤 {title}")
        print(f"   嘉宾: {interview['guest']} ({interview['role']})")
        print(f"   主题: {interview['topic']}")
        print(f"   时间: {pub_date}")
        
        try:
            response = requests.post(f"{BASE_URL}/create_podcast_item", json=payload)
            if response.status_code == 200:
                print(f"   ✅ 访谈条目创建成功")
            else:
                print(f"   ❌ 创建失败: {response.json()}")
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

if __name__ == "__main__":
    print("🎙️ Podcast API 使用示例")
    print("=" * 50)
    
    # 检查服务器状态
    try:
        response = requests.get(f"{BASE_URL}/sd_webui_status", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行，请先启动服务器")
            exit(1)
        print("✅ 服务器运行正常")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        exit(1)
    
    print("\n选择示例:")
    print("1. 创建单个播客条目")
    print("2. 创建一周播客系列")
    print("3. 创建AI主题系列")
    print("4. 创建访谈系列")
    print("5. 运行所有示例")
    
    choice = input("\n请输入选择 (1-5): ").strip()
    
    if choice == "1":
        create_single_episode()
    elif choice == "2":
        create_weekly_series()
    elif choice == "3":
        create_themed_episodes()
    elif choice == "4":
        create_interview_series()
    elif choice == "5":
        print("\n🚀 运行所有示例...")
        create_single_episode()
        create_weekly_series()
        create_themed_episodes()
        create_interview_series()
    else:
        print("无效选择，运行单个条目示例...")
        create_single_episode()
    
    print("\n🎉 示例运行完成！")
    print("\n📋 后续步骤:")
    print("   1. 检查 GitHub 仓库: **************:fjxc2021/podcast-feed.git")
    print("   2. 验证 podcast.xml 文件内容")
    print("   3. 测试 RSS feed 在播客应用中的显示效果")
    print("   4. 确认音频文件URL的可访问性")
