#!/usr/bin/env python3
"""
清理 GitHub 仓库中的测试文件
"""

import os
import subprocess
import shutil

# 配置
GITHUB_REPO_URL = "**************:fjxc2021/podcast-feed.git"
PODCAST_REPO_DIR = r"D:\temp\podcast-feed"

def get_git_env():
    """获取 Git 操作所需的环境变量"""
    env = os.environ.copy()
    user_home = os.path.expanduser("~")
    ssh_dir = os.path.join(user_home, ".ssh")
    
    env["HOME"] = user_home
    env["SSH_AUTH_SOCK"] = env.get("SSH_AUTH_SOCK", "")
    
    if os.name == 'nt':  # Windows
        key_types = ["id_ed25519", "id_rsa", "id_ecdsa"]
        ssh_key_path = None
        
        for key_type in key_types:
            potential_key = os.path.join(ssh_dir, key_type)
            if os.path.exists(potential_key):
                ssh_key_path = potential_key
                break
        
        if ssh_key_path:
            env["GIT_SSH_COMMAND"] = f'ssh -i "{ssh_key_path}" -o StrictHostKeyChecking=no'
            print(f"🔑 使用 SSH 密钥: {ssh_key_path}")
    
    return env

def cleanup_test_files():
    """清理测试文件"""
    print("🧹 清理 GitHub 仓库中的测试文件")
    print("=" * 50)
    
    if not os.path.exists(PODCAST_REPO_DIR):
        print(f"❌ 仓库目录不存在: {PODCAST_REPO_DIR}")
        return False
    
    try:
        env = get_git_env()
        
        # 1. 进入仓库目录
        print(f"📁 进入仓库目录: {PODCAST_REPO_DIR}")
        
        # 2. 列出所有文件，查找测试文件
        files = os.listdir(PODCAST_REPO_DIR)
        print(f"📂 当前文件: {files}")
        
        # 3. 查找并删除测试文件
        test_files_to_remove = []
        
        for file in files:
            file_path = os.path.join(PODCAST_REPO_DIR, file)
            if os.path.isfile(file_path):
                # 检查是否是测试文件
                if any(keyword in file.lower() for keyword in ['test', 'debug', 'temp']):
                    if file.endswith(('.xml', '.txt')):
                        test_files_to_remove.append(file)
                        print(f"🗑️ 发现测试文件: {file}")
        
        if not test_files_to_remove:
            print("✅ 没有发现需要删除的测试文件")
            return True
        
        # 4. 删除测试文件
        for file in test_files_to_remove:
            file_path = os.path.join(PODCAST_REPO_DIR, file)
            try:
                os.remove(file_path)
                print(f"🗑️ 已删除: {file}")
            except Exception as e:
                print(f"❌ 删除失败 {file}: {e}")
        
        # 5. Git 操作
        print("\n📤 提交删除操作到 Git...")
        
        # Git add
        result = subprocess.run(
            ["git", "add", "."],
            cwd=PODCAST_REPO_DIR,
            capture_output=True,
            text=True,
            timeout=15,
            env=env
        )
        
        if result.returncode != 0:
            print(f"❌ Git add 失败: {result.stderr}")
            return False
        
        # Git commit
        commit_message = f"Remove test files: {', '.join(test_files_to_remove)}"
        result = subprocess.run(
            ["git", "commit", "-m", commit_message],
            cwd=PODCAST_REPO_DIR,
            capture_output=True,
            text=True,
            timeout=30,
            env=env
        )
        
        if result.returncode != 0:
            print(f"⚠️ Git commit 失败 (可能没有变更): {result.stderr}")
            return True  # 没有变更也算成功
        
        print(f"✅ Git commit 成功: {result.stdout}")
        
        # Git push
        result = subprocess.run(
            ["git", "push"],
            cwd=PODCAST_REPO_DIR,
            capture_output=True,
            text=True,
            timeout=60,
            env=env
        )
        
        if result.returncode == 0:
            print("✅ Git push 成功")
            print(f"🗑️ 已删除测试文件: {', '.join(test_files_to_remove)}")
            return True
        else:
            print(f"❌ Git push 失败: {result.stderr}")
            return False
        
    except subprocess.TimeoutExpired:
        print("❌ Git 操作超时")
        return False
    except Exception as e:
        print(f"❌ 清理操作异常: {e}")
        return False

def list_current_files():
    """列出当前仓库中的文件"""
    print("\n📂 当前仓库文件列表:")
    print("-" * 30)
    
    if not os.path.exists(PODCAST_REPO_DIR):
        print(f"❌ 仓库目录不存在: {PODCAST_REPO_DIR}")
        return
    
    try:
        files = os.listdir(PODCAST_REPO_DIR)
        for file in files:
            file_path = os.path.join(PODCAST_REPO_DIR, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"📄 {file} ({size} 字节)")
            elif os.path.isdir(file_path) and not file.startswith('.'):
                print(f"📁 {file}/")
    except Exception as e:
        print(f"❌ 列出文件失败: {e}")

if __name__ == "__main__":
    print("🧹 GitHub 仓库测试文件清理工具")
    print("=" * 60)
    
    # 先列出当前文件
    list_current_files()
    
    # 询问确认
    print("\n⚠️ 即将删除所有包含 'test'、'debug'、'temp' 关键词的 .xml 和 .txt 文件")
    confirm = input("确认要继续吗？(y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        success = cleanup_test_files()
        
        if success:
            print("\n🎉 清理完成！")
            print("✅ 测试文件已从 GitHub 仓库中删除")
            
            # 再次列出文件确认
            list_current_files()
        else:
            print("\n❌ 清理失败，请检查错误信息")
    else:
        print("\n❌ 操作已取消")
