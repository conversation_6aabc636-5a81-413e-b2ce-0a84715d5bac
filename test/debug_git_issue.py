#!/usr/bin/env python3
"""
调试 Git 操作问题
"""

import os
import subprocess
import tempfile
import shutil

# 配置
GITHUB_REPO_URL = "**************:fjxc2021/podcast-feed.git"
PODCAST_REPO_DIR = r"D:\temp\podcast-feed"

def get_git_env():
    """获取 Git 操作所需的环境变量"""
    env = os.environ.copy()
    
    # 确保 SSH 相关环境变量
    user_home = os.path.expanduser("~")
    ssh_dir = os.path.join(user_home, ".ssh")
    
    # 设置 SSH 相关环境变量
    env["HOME"] = user_home
    env["SSH_AUTH_SOCK"] = env.get("SSH_AUTH_SOCK", "")
    
    # 如果是 Windows，可能需要设置 GIT_SSH_COMMAND
    if os.name == 'nt':  # Windows
        # 尝试多种密钥类型
        key_types = ["id_ed25519", "id_rsa", "id_ecdsa"]
        ssh_key_path = None
        
        for key_type in key_types:
            potential_key = os.path.join(ssh_dir, key_type)
            if os.path.exists(potential_key):
                ssh_key_path = potential_key
                break
        
        if ssh_key_path:
            env["GIT_SSH_COMMAND"] = f'ssh -i "{ssh_key_path}" -o StrictHostKeyChecking=no'
            print(f"🔑 使用 SSH 密钥: {ssh_key_path}")
        else:
            print(f"⚠️ 未找到 SSH 密钥在: {ssh_dir}")
    
    return env

def test_ssh_connection():
    """测试 SSH 连接"""
    print("🔍 测试 SSH 连接到 GitHub...")
    
    try:
        env = get_git_env()
        result = subprocess.run(
            ["ssh", "-T", "**************"],
            capture_output=True,
            text=True,
            timeout=10,
            env=env
        )
        
        print(f"📤 SSH 测试返回码: {result.returncode}")
        print(f"📥 SSH 测试输出: {result.stderr}")
        
        if "successfully authenticated" in result.stderr:
            print("✅ SSH 连接成功")
            return True
        else:
            print("❌ SSH 连接失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ SSH 连接超时")
        return False
    except Exception as e:
        print(f"❌ SSH 测试异常: {e}")
        return False

def test_git_clone():
    """测试 Git 克隆"""
    print("\n📥 测试 Git 克隆...")
    
    # 使用临时目录避免冲突
    temp_dir = r"D:\temp\test-git-clone"
    
    # 清理现有目录
    if os.path.exists(temp_dir):
        print(f"🗑️ 清理现有目录: {temp_dir}")
        shutil.rmtree(temp_dir)
    
    try:
        env = get_git_env()
        
        print(f"📥 克隆仓库到: {temp_dir}")
        print(f"📥 仓库URL: {GITHUB_REPO_URL}")
        
        # 确保父目录存在
        os.makedirs(os.path.dirname(temp_dir), exist_ok=True)
        
        result = subprocess.run(
            ["git", "clone", GITHUB_REPO_URL, temp_dir],
            capture_output=True,
            text=True,
            timeout=60,
            env=env
        )
        
        print(f"📥 Git clone 返回码: {result.returncode}")
        print(f"📥 Git clone 输出: {result.stdout}")
        print(f"📥 Git clone 错误: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ Git 克隆成功")
            
            # 检查克隆的内容
            if os.path.exists(temp_dir):
                files = os.listdir(temp_dir)
                print(f"📂 克隆的文件: {files}")
            
            return temp_dir
        else:
            print("❌ Git 克隆失败")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ Git 克隆超时")
        return None
    except Exception as e:
        print(f"❌ Git 克隆异常: {e}")
        return None

def test_git_operations(repo_dir):
    """测试 Git 提交和推送"""
    print(f"\n📝 测试 Git 操作在: {repo_dir}")
    
    if not repo_dir or not os.path.exists(repo_dir):
        print("❌ 仓库目录不存在")
        return False
    
    try:
        env = get_git_env()
        
        # 创建一个测试文件
        test_file = os.path.join(repo_dir, "test_debug.txt")
        with open(test_file, "w", encoding="utf-8") as f:
            f.write(f"Git 调试测试 - {os.getpid()}\n")
        print(f"📄 创建测试文件: {test_file}")
        
        # Git add
        print("📤 执行 git add...")
        result = subprocess.run(
            ["git", "add", "."],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            timeout=15,
            env=env
        )
        print(f"git add 返回码: {result.returncode}")
        if result.stderr:
            print(f"git add 错误: {result.stderr}")
        
        if result.returncode != 0:
            print("❌ Git add 失败")
            return False
        
        # Git commit
        print("📤 执行 git commit...")
        commit_message = "Debug test commit"
        result = subprocess.run(
            ["git", "commit", "-m", commit_message],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            timeout=30,
            env=env
        )
        print(f"git commit 返回码: {result.returncode}")
        print(f"git commit 输出: {result.stdout}")
        if result.stderr:
            print(f"git commit 错误: {result.stderr}")
        
        if result.returncode != 0:
            print("❌ Git commit 失败")
            return False
        
        # Git push
        print("📤 执行 git push...")
        result = subprocess.run(
            ["git", "push"],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            timeout=60,
            env=env
        )
        print(f"git push 返回码: {result.returncode}")
        print(f"git push 输出: {result.stdout}")
        if result.stderr:
            print(f"git push 错误: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ Git push 成功")
            return True
        else:
            print("❌ Git push 失败")
            return False
            
    except subprocess.TimeoutExpired as e:
        print(f"❌ Git 操作超时: {e}")
        return False
    except Exception as e:
        print(f"❌ Git 操作异常: {e}")
        return False

def main():
    print("🔧 Git 操作调试工具")
    print("=" * 60)
    
    # 1. 测试 SSH 连接
    ssh_success = test_ssh_connection()
    
    if not ssh_success:
        print("\n❌ SSH 连接失败，无法继续 Git 操作")
        print("💡 请检查:")
        print("   1. SSH 密钥是否正确配置")
        print("   2. 密钥是否已添加到 GitHub")
        print("   3. 网络连接是否正常")
        return
    
    # 2. 测试 Git 克隆
    repo_dir = test_git_clone()
    
    if not repo_dir:
        print("\n❌ Git 克隆失败，无法继续")
        return
    
    # 3. 测试 Git 操作
    if test_git_operations(repo_dir):
        print("\n🎉 所有 Git 操作测试成功！")
        print("✅ SSH 连接正常")
        print("✅ Git 克隆正常")
        print("✅ Git 提交推送正常")
        print("\n💡 Git 配置没有问题，可能是服务器代码的其他问题")
    else:
        print("\n❌ Git 操作测试失败")
        print("💡 请检查 Git 配置和权限")

if __name__ == "__main__":
    main()
