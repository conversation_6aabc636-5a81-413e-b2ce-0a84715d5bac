# Podcast RSS Feed API 文档

本文档描述了用于创建和管理 Podcast RSS Feed 的 API 接口。

## 接口概览

| 接口 | 方法 | 功能 | 状态 |
|------|------|------|------|
| `/create_podcast_item` | POST | 创建新的 podcast 条目并推送到 GitHub | ✅ 新增 |

## 详细接口说明

### 创建 Podcast 条目 - `/create_podcast_item`

**请求方法**: `POST`  
**功能**: 创建新的 podcast 条目，更新 RSS feed，并自动推送到 GitHub 仓库

**请求体**:
```json
{
  "title": "第1期：AI 让你不焦虑",
  "description": "本期内容讲述AI如何帮助内容创作者减压",
  "enclosure_url": "https://yourdomain.com/audio/ep1.mp3",
  "pub_date": "Mon, 01 Jul 2025 12:00:00 +0800",
  "length": "12345678"
}
```

**参数说明**:
- `title` (必需): 播客条目标题
- `description` (必需): 播客条目描述
- `enclosure_url` (必需): 音频文件的 URL
- `pub_date` (必需): 发布日期，RFC 2822 格式
- `length` (可选): 音频文件大小（字节），默认 "12345678"

**成功响应**:
```json
{
  "status": "success",
  "message": "Podcast 条目创建并推送成功",
  "title": "第1期：AI 让你不焦虑",
  "xml_file": "D:\\temp\\podcast-feed\\podcast.xml",
  "repository": "**************:fjxc2021/podcast-feed.git"
}
```

**错误响应**:
```json
{
  "error": "缺少必需参数: title"
}
```

## 工作流程

### 1. 自动化流程

当调用 `/create_podcast_item` 接口时，系统会自动执行以下步骤：

1. **验证参数**: 检查所有必需参数是否提供
2. **Git 操作**: 克隆或拉取最新的 GitHub 仓库
3. **XML 处理**: 
   - 如果 `podcast.xml` 不存在，创建新的 RSS feed 模板
   - 如果文件存在，解析现有内容
   - 添加新的 `<item>` 条目到 RSS feed 顶部
4. **文件更新**: 格式化并保存更新后的 XML 文件
5. **Git 提交**: 提交更改并推送到远程仓库

### 2. RSS Feed 结构

生成的 `podcast.xml` 文件遵循标准的 RSS 2.0 格式，包含 iTunes 扩展：

```xml
<?xml version="1.0" ?>
<rss version="2.0" xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd">
  <channel>
    <title>我的播客</title>
    <description>这是一个关于技术和生活的播客</description>
    <link>https://yourdomain.com</link>
    <language>zh-CN</language>
    <copyright>© 2025 我的播客</copyright>
    <lastBuildDate>Wed, 31 Jul 2025 15:30:00 +0800</lastBuildDate>
    <itunes:author>播客作者</itunes:author>
    <itunes:summary>这是一个关于技术和生活的播客</itunes:summary>
    <itunes:category text="Technology"/>
    
    <item>
      <title>第1期：AI 让你不焦虑</title>
      <description>本期内容讲述AI如何帮助内容创作者减压</description>
      <enclosure url="https://yourdomain.com/audio/ep1.mp3" length="12345678" type="audio/mpeg"/>
      <pubDate>Mon, 01 Jul 2025 12:00:00 +0800</pubDate>
      <guid isPermaLink="true">https://yourdomain.com/audio/ep1.mp3</guid>
    </item>
  </channel>
</rss>
```

## 使用示例

### cURL 示例

```bash
curl -X POST http://127.0.0.1:7899/create_podcast_item \
  -H "Content-Type: application/json" \
  -d '{
    "title": "第1期：AI 让你不焦虑",
    "description": "本期内容讲述AI如何帮助内容创作者减压",
    "enclosure_url": "https://yourdomain.com/audio/ep1.mp3",
    "pub_date": "Mon, 01 Jul 2025 12:00:00 +0800",
    "length": "12345678"
  }'
```

### Python 示例

```python
import requests
from datetime import datetime

# 生成当前时间的 RFC 2822 格式
pub_date = datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0800")

payload = {
    "title": "第1期：AI 让你不焦虑",
    "description": "本期内容讲述AI如何帮助内容创作者减压",
    "enclosure_url": "https://yourdomain.com/audio/ep1.mp3",
    "pub_date": pub_date,
    "length": "12345678"
}

response = requests.post(
    "http://127.0.0.1:7899/create_podcast_item",
    json=payload
)

print(response.json())
```

### JavaScript 示例

```javascript
const payload = {
  title: "第1期：AI 让你不焦虑",
  description: "本期内容讲述AI如何帮助内容创作者减压",
  enclosure_url: "https://yourdomain.com/audio/ep1.mp3",
  pub_date: new Date().toUTCString().replace('GMT', '+0800'),
  length: "12345678"
};

fetch('http://127.0.0.1:7899/create_podcast_item', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(payload)
})
.then(response => response.json())
.then(data => console.log(data));
```

## 配置说明

### 服务器配置

在 `cmd_server.py` 中的相关配置：

```python
# Podcast 相关配置
GITHUB_REPO_URL = "**************:fjxc2021/podcast-feed.git"
PODCAST_REPO_DIR = r'D:\temp\podcast-feed'
PODCAST_XML_FILE = "podcast.xml"
```

### RSS Feed 模板配置

可以修改 `create_podcast_xml_template()` 函数来自定义 RSS feed 的基本信息：

- 播客标题和描述
- 作者信息
- 分类标签
- 版权信息

## 测试

### 运行测试脚本

```bash
# 基本功能测试
python test/test_podcast_api.py

# 使用示例
python test/podcast_example.py
```

### 测试场景

1. **单个条目创建**: 测试基本的条目创建功能
2. **批量创建**: 测试创建多个条目
3. **错误处理**: 测试参数验证和错误情况
4. **Git 集成**: 验证 GitHub 推送功能

## 注意事项

### 1. Git 配置要求

- 确保系统已安装 Git
- 配置 SSH 密钥以访问 GitHub 仓库
- 确保有仓库的写入权限

### 2. 文件路径

- `PODCAST_REPO_DIR` 目录会自动创建
- 确保有足够的磁盘空间
- Windows 路径使用原始字符串 (r'path')

### 3. 时间格式

- `pub_date` 必须使用 RFC 2822 格式
- 建议包含时区信息 (+0800)
- 可以使用 Python 的 `strftime` 生成

### 4. 音频文件

- `enclosure_url` 必须是可访问的 HTTP/HTTPS URL
- 建议使用 MP3 格式 (type="audio/mpeg")
- `length` 参数应该是实际文件大小

## 故障排除

### 常见问题

1. **Git 操作失败**
   - 检查 SSH 密钥配置
   - 验证仓库 URL 和权限
   - 确保网络连接正常

2. **XML 解析错误**
   - 检查现有 XML 文件格式
   - 验证特殊字符是否正确转义

3. **参数验证失败**
   - 确保所有必需参数都已提供
   - 检查参数格式是否正确

### 日志查看

服务器会输出详细的操作日志，包括：
- Git 操作状态
- XML 文件处理过程
- 错误信息和堆栈跟踪

## 扩展功能

### 可能的增强

1. **批量导入**: 支持一次性导入多个条目
2. **条目编辑**: 支持修改现有条目
3. **条目删除**: 支持删除指定条目
4. **模板自定义**: 支持自定义 RSS feed 模板
5. **多仓库支持**: 支持推送到多个 Git 仓库
